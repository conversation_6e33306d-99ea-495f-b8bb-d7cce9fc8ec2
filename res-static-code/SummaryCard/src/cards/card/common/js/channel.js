import MessageChannel from '@hap.io.MessageChannel'
export default class Channel {
  /**
   * constructor of Channel
   * @param {*} tag for output logs
   * @param {*} msgCallback subscribe message from channel
   * @param {*} channelOpenCallback channel open success, channel ready for send message
   */
  constructor(tag, msgCallback, channelOpenCallback, channelErrorCallback, channelCloseCallback) {
    this.tag = tag
    this.msgCallback = msgCallback
    this.channelOpenCallback = channelOpenCallback
    this.channelErrorCallback = channelErrorCallback
    this.channelCloseCallback = channelCloseCallback
    this.channel = null
    this.isConnected = false
    this.cachedReqList = []
    this.createChannel()
  }

  /**
   * create channel
   * @param {boolean} reopen is reopen, if true, need to send cached request
   */
  async createChannel(reopen) {
    console.log('开始创建channel通道')
    this.channel = new MessageChannel({
      package: 'com.coloros.soundrecorder', // todo: 替换为对应APP包名
      sign: '64aafaf1d5bc9155a9e417a849e4f8eda1d0d1341667c28ed7c443c76f820b9a', // todo: 替换为对应APP签名 
      fail: (err, code) => {
        this.isConnected = false
        console.error(
          `${this.tag} Channel construct fail err: ${JSON.stringify(err)}`
        )
        this.channelErrorCallback && this.channelErrorCallback()
      }
    })
    console.log('---this.channel---', JSON.stringify(this.channel));
    // channel通道打开的回调
    this.channel.onopen = () => {
      this.isConnected = true
      console.log('hannel实例Id open', this.channel._instId)
      console.log(`${this.tag} Channel onopen, reopen=${!!reopen}`)
      if (reopen) { // recreate success, send cached msg
        this.clearCachedReqList()
      } else {
        this.channelOpenCallback && this.channelOpenCallback()
      }
    }
    // channel关闭的回调
    this.channel.onclose = data => {
      console.log('hannel实例Id close', this.channel._instId)
      this.isConnected = false
      console.log(
        `${this.tag} Channel onclose data.code = ${data?.code}, data.reason = ${data?.reason}`
      )
      if (this.channelCloseCallback) {
        this.channelCloseCallback()
      }
    }
    // channel连接出错的回调
    this.channel.onerror = res => {
      console.log('hannel实例Id error', this.channel._instId)
      this.isConnected = false
      const { code, data } = res
      console.error(
        `${this.tag} Channel onerror code = ${code}, data = ${data}`
      )
      if (![1000, 1001, 1002, 1003, 1004, 1006, 1007, 1008, 1009, 1010].includes(code)) {
        this.channelErrorCallback && this.channelErrorCallback(code, data)
      }
    }
    // 接受消息的回调
    this.channel.onmessage = data => {
      console.log('hannel实例Id', this.channel._instId)
      console.log(
        `${this.tag} Channel onmessage code: ${data?.message?.code}, data: ${data?.message?.data}`
      )
      if (typeof this.msgCallback === 'function') {
        // 根据返回数据结构自行处理
        const { code, data: msgData } = data?.message || {}
        let msgDataObj = msgData || {}
        if (msgData && typeof msgData === 'string') {
          try {
            msgDataObj = JSON.parse(msgData) // todo：根据实际情况解析数据，可以让APP返回请求携带的random，用于匹配对应的请求
          } catch (err) {
            console.error(
              `${this.tag} Channel onmessage error: ${JSON.stringify(err)}`
            )
          }
        }
        this.msgCallback({
          code,
          msgData: data?.message?.data
        })
      }
    }
  }
  // 清除缓存的回调
  clearCachedReqList() {
    const reqArr = [...this.cachedReqList];
    console.log(`${this.tag} Channel clearCachedReqList, count=${reqArr.length}`);
    this.cachedReqList = [];
    reqArr.forEach(req => {
      const { reqId, sendData, code, resolve, reject } = req;
      const random = this.getReqRandom(reqId);
      if (!this.isConnected) {
        console.error(`${this.tag} Channel send cache, connect error! random=${random}`);
        reject({ code: -1, message: 'Channel still closed' }); // 立即拒绝
        return;
      }
      this.channel.send({
        message: {
          code: code, // 使用原始 code 值
          data: JSON.stringify({ random, sendData }) // 保持与之前格式一致
        },
        success: (data) => {
          console.log(`${this.tag} Channel 缓存发送成功：${random}, ${sendData}, ${code}`);
          resolve(data); // 触发原 Promise 的 resolve
        },
        fail: (error, code) => {
          console.error(`${this.tag} Channel 缓存发送失败：${random}`);
          reject({ error, code }); // 触发原 Promise 的 reject
        }
      });
    });
  }
  // 往缓存里面添加异步操作
  addReqToCachedList(req) {
    this.cachedReqList.push(req) // todo: filter duplicated msg
  }
  // 获取随机数
  getReqRandom(reqId) {
    const r = reqId || Math.random().toString(36).slice(4)
    // return `${funcName}-${r}`
    return `${r}`
  }

  /**
   * send message to app by channel
   * {stirng} funcName function name
   * @param {string} reqId unique reqeust id
   * @param {object} data reqeust params
   */
  async send(reqId, sendData = null, code = 0) {
    console.log('Channel实例Id send', this.channel._instId)
    console.log(`${this.tag} Channel send, reqId=${reqId}, sendData=${JSON.stringify(sendData)},code=${code}`)
    if (!this.isConnected) {
      console.error(
        `${this.tag} Channel send, connect error! isConnected=${this.isConnected}, start reopen,code =${code}`
      )
      // 开始重新创建channel
      console.log('开始重新创建channel, channel.js 方法里面 send');
      this.createChannel(true)
      return new Promise((resolve, reject) => {
        this.addReqToCachedList({ reqId, sendData, code, resolve, reject });
      })
    }
    return new Promise((resolve, reject) => {
      const random = this.getReqRandom(reqId)
      this.channel.send({
        message: {
          code: code,
          data: sendData,
        },
        success: data => {
          console.log(
            `${this.tag} Channel 发送成功：${random}, ${JSON.stringify(sendData)},data=${data}`
          )
          resolve(data)
        },
        fail: (error, code) => {
          console.error(
            `${this.tag
            } Channel 发送失败：${random}, ${code}, ${JSON.stringify(error)}`
          )
          reject({ error, code })
        }
      })
    })
  }

  close() {
    console.log(`${this.tag} Channel close in channel.js`)
    this.channel?.close({
      reason: `${this.tag} close Channel`,
      success: data => {
        console.log(`${this.tag} Channel 关闭成功：${JSON.stringify(data)}`)
      },
      fail: (error, code) => {
        console.error(
          `${this.tag} Channel 关闭失败：code=${code}, ${JSON.stringify(
            error
          )}`
        )
      }
    })
  }
}
