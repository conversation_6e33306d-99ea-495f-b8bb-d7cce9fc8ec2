/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/01/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.playback.newconvert.view;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class TextImageMixLayoutTest {

    @Mock
    private Context mContext;
    private TextImageMixLayout mixLayoutHelper;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mixLayoutHelper = new TextImageMixLayout(mContext);
    }

    @Test
    public void should_empty_when_setConvertContentItem() {
        ConvertContentItem item = new ConvertContentItem();
        item.setStartTime(1000);
        item.setEndTime(10000);
        item.setTextContent("虎式工哈哈哈哈哈哈卡盟民京哈空间很大空间撒谎哈口水都快摩卡金");
        item.setRoleName("wo ce shi");
        item.setRoleId(100);
        ArrayList<ConvertContentItem.ItemMetaData> itemMetaDatas = new ArrayList<>();
        ArrayList<ConvertContentItem.SubSentence> sentences = new ArrayList<>();
        ConvertContentItem.SubSentence sentence = new ConvertContentItem.SubSentence(0, 9,
                0, "测试测试测试测试测试测试", true, false, null);
        sentences.add(sentence);
        ConvertContentItem.ItemMetaData itemMetaData = new ConvertContentItem.TextItemMetaData(sentences);
        itemMetaDatas.add(itemMetaData);
        item.setMTextOrImageItems(itemMetaDatas);
        mixLayoutHelper.setConvertContentItem(item, false, 0);
    }

    @After
    public void tearDown() {
        mContext = null;
        mixLayoutHelper = null;
    }
}
