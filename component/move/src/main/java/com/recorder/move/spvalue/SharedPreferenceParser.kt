/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SharedPreferenceParser
 * Description:
 * Version: 1.0
 * Date: 2023/8/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/31 1.0 create
 */

package com.recorder.move.spvalue

import com.recorder.move.BaseXmlParser
import com.recorder.move.MoveUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.DebugUtil

class SharedPreferenceParser : BaseXmlParser<Unit>() {
    override fun getXmlName(): String = SharedPreferenceComposer.SHARED_PREFERENCE_XML_NAME

    override fun createData(): Unit {
        return Unit
    }

    override fun setDataAttr(data: Unit, name: String, value: String) {
        if (MoveUtils.checkCanBackUserNotice()) {
            DebugUtil.i("SharedPreferenceParser", "setDataAttr,name=$name,value=$value")
            value.toIntOrNull()?.let {
                StorageManager.setIntPref(BaseApplication.getAppContext(), name, it)
            }
        }
    }
}