/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :  分组表搬家相关类
 * * Version     : 1.0
 * * Date        : 2025/02/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.recorder.move

import android.util.Xml
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.databean.GroupInfo
import org.xmlpull.v1.XmlSerializer
import java.io.IOException
import java.io.StringWriter

class GroupInfoXmlComposer {
    companion object {
        const val TAG = "GroupInfoXmlComposer"
    }

    private var mSerializer: XmlSerializer? = null
    private var mStringWriter: StringWriter? = null

    @Suppress("TooGenericExceptionCaught")
    fun startCompose(): Boolean {
        var result = false
        mSerializer = Xml.newSerializer()
        mStringWriter = StringWriter()
        try {
            mSerializer!!.setOutput(mStringWriter)
            mSerializer!!.startDocument(null, false)
            mSerializer!!.startTag("", "groupInfo")
            result = true
        } catch (e: IOException) {
            DebugUtil.e(TAG, "startCompose IOException error", e)
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "startCompose IllegalArgumentException error", e)
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "startCompose IllegalStateException error", e)
        }
        return result
    }

    @Suppress("TooGenericExceptionCaught")
    fun endCompose(): Boolean {
        var result = false
        try {
            mSerializer?.endTag("", "groupInfo")
            mSerializer?.endDocument()
            result = true
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "endCompose IllegalArgumentException error", e)
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "endCompose IllegalStateException error", e)
        } catch (e: IOException) {
            DebugUtil.e(TAG, "endCompose IOException error", e)
        }
        return result
    }

    @Suppress("TooGenericExceptionCaught")
    fun getXmlInfo(): String? {
        try {
            if (mStringWriter != null) {
                val info = mStringWriter.toString()
                mStringWriter!!.close()
                return info
            }
        } catch (e: IOException) {
            DebugUtil.e(TAG, "mStringWriter close IOException error", e)
        }
        return null
    }

    @Suppress("TooGenericExceptionCaught")
    fun addGroupInfo(groupInfo: GroupInfo) {
        try {
            mSerializer?.startTag("", DatabaseConstant.ROOT)
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID, covertToString(groupInfo.mId))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID, covertToString(groupInfo.mUuId))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME, covertToString(groupInfo.mGroupName))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_SORT, covertToString(groupInfo.mGroupSort))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_COLOR, covertToString(groupInfo.mGroupColor))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_COUNT, covertToString(groupInfo.mGroupCount))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID, covertToString(groupInfo.mGroupGlobalId))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY, covertToString(groupInfo.mDirty))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DELETED, covertToString(groupInfo.mIsDeleted))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_IS_PRIVATE, covertToString(groupInfo.mIsPrivate))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_CLOUD_SYS_VERSION, covertToString(groupInfo.sysVersion))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_TYPE, covertToString(groupInfo.mGroupType))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_TYPE, covertToString(groupInfo.mGroupSort))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS, covertToString(groupInfo.syncUploadStatus))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_DOWNLOAD_STATUS,
                covertToString(groupInfo.syncDownloadStatus))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ERROR_CODE, covertToString(groupInfo.errorCode))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_LOCAL_EDIT_STATUS, covertToString(groupInfo.editStatus))
            mSerializer?.attribute("", DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_DATE, covertToString(groupInfo.syncDate))
            mSerializer?.endTag("", DatabaseConstant.ROOT)
            DebugUtil.i(TAG, "addGroupInfo: $groupInfo")
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "addGroupInfo IllegalArgumentException error", e)
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "addGroupInfo IllegalStateException error", e)
        } catch (e: IOException) {
            DebugUtil.e(TAG, "addGroupInfo IOException error", e)
        } catch (e: NullPointerException) {
            DebugUtil.e(TAG, "addGroupInfo NullPointerException error", e)
        }
    }
    private fun covertToString(v: Any?): String {
        if (v != null) {
            return v.toString()
        }
        return ""
    }
}