/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: RecorderXmlParser
 ** Description:
 ** Version:
 ** Date :
 ** Author: chenlipeng W9001067
 **
 ** v1.0, 2019-3-12, chenlipeng W9001067, create
 ****************************************************************/
// OPLUS Java File Skip Rule:NestedBranchDepth
package com.recorder.move;

import android.os.Environment;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;
import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.util.HashSet;

import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.common.db.PictureMarkDbUtils;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.utils.RecordModeUtil;

public class RecorderXmlParser {
    public static final String CHARSET_ISO_8859_1 = "ISO-8859-1";
    private final static String TAG = "RecorderXmlParser";

    public static HashSet<Record> parse(String recordString) {
        Record record = null;
        HashSet<Record> list = new HashSet<>();
        try {
            XmlPullParserFactory factory = XmlPullParserFactory.newInstance();
            XmlPullParser parser = factory.newPullParser();
            parser.setInput(new StringReader(recordString));

            int eventType = parser.getEventType();
            String tagName = "";
            while (eventType != XmlPullParser.END_DOCUMENT) {
                switch (eventType) {
                    case XmlPullParser.START_DOCUMENT:
                        break;
                    case XmlPullParser.START_TAG:
                        record = new Record();
                        tagName = parser.getName();
                        int recorderType = -1;
                        if (tagName.equals(DatabaseConstant.ROOT)) {
                            int attrNum = parser.getAttributeCount();
                            for (int i = 0; i < attrNum; ++i) {
                                String name = parser.getAttributeName(i);
                                String value = parser.getAttributeValue(i);

                                switch (name) {
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID:
                                        record.setUuid(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA:
                                        record.setData(MoveUtils.getDataByData(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE:
                                        record.setFileSize(Long.parseLong(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME:
                                        record.setDisplayName(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE:
                                        record.setMimeType(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_CREATED:
                                        record.setDateCreated(Long.parseLong(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED:
                                        record.setDateModied(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_RECORD_TYPE:
                                        recorderType = Integer.parseInt(value);
                                        record.setRecordType(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA:
                                        record.setMarkData(value.getBytes(CHARSET_ISO_8859_1));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_DATA:
                                        record.setAmpData(value.getBytes(CHARSET_ISO_8859_1));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION:
                                        record.setDuration(Long.parseLong(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_DISPLAY_NAME:
                                        record.setBuckedDisplayName(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY:
                                        record.setDirty(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE:
                                        record.setDeleted(Boolean.parseBoolean(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5:
                                        record.setMD5(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID:
                                        record.setFileId(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID:
                                        record.setGlobalId(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE:
                                        record.setSyncType(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS:
                                        record.setSyncUploadStatus(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLOUM_NAME_SYNC_DOWNLOAD_STATUS:
                                        record.setSyncDownlodStatus(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_ERROR_CODE:
                                        record.setErrorCode(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_LEVEL:
                                        record.setLevel(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS:
                                        record.setEditStatus(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_DATE:
                                        record.setSyncDate(Long.parseLong(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_FAIL_COUNT:
                                        record.setFailedCount(Long.parseLong(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME:
                                        record.setLastFailedTime(Long.parseLong(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH: {
                                        if (BaseUtil.isAndroidQOrLater()) {
                                            if ((value != null) && (value.contains(RecordModeConstant.STORAGE_RECORD_ABOVE_Q))) {
                                                record.setRelativePath(value);
                                            } else {
                                                if (recorderType != -1) {
                                                    record.setRelativePath(RecordModeUtil.getRelativePathByRecordType(recorderType, true));
                                                }
                                            }
                                        } else {
                                            if ((value != null) && (value.contains(RecordModeConstant.STORAGE_RECORD_ABOVE_Q))) {
                                                record.setRelativePath(value.replace((Environment.DIRECTORY_MUSIC + File.separator), ""));
                                            } else {
                                                record.setRelativePath(value);
                                            }
                                        }
                                        break;
                                    }
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH:
                                        record.setAmpFilePath(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS:
                                        record.setSyncPrivateStatus(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_MIGRATE_STATUS:
                                        record.setSyncMigrateStatus(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_MARKLIST_SHOWING:
                                        record.setIsMarkListShowing(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION:
                                        record.setSysVersion(Long.parseLong(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_CHECK_PAYLOAD:
                                        record.setCheckPayload(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_DIRECT_ON:
                                        record.setDirectOn(Boolean.parseBoolean(value));
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRECT_TIME:
                                        record.setDirectTime(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_UUID:
                                        record.setGroupUuid(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_CALLER_NAME:
                                        record.setCallerName(value);
                                        break;
                                    case DatabaseConstant.RecorderColumn.COLUMN_NAME_ORIGINAL_NAME:
                                        record.setOriginalName(value);
                                        break;
                                    default:
                                        break;
                                    case PictureMarkDbUtils.TEMP_ID:
                                        record.setTempId(value);
                                        break;
                                }
                            }
                        }
                        break;

                    case XmlPullParser.END_TAG:
                        if (parser.getName().equals(DatabaseConstant.ROOT) && (record != null)) {
                            list.add(record);
                        }
                        break;
                    default:
                        break;
                }

                eventType = parser.next();
            }
        } catch (XmlPullParserException | IOException e) {
            e.printStackTrace();
        }

        return list;
    }
}