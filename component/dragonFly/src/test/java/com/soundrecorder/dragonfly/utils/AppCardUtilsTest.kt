/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardUtilsTest
 Description:
 Version: 1.0
 Date: 2022/11/23
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.dragonfly.utils

import android.content.Context
import android.os.Build
import android.os.SystemClock
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.dragonfly.utils.AppCardUtils.appCtx
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.Mockito.anyInt
import org.mockito.Mockito.anyString
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.spy
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [])
class AppCardUtilsTest {

    @Test
    fun openDebugTest() {
        AppCardUtils.openDebug()
    }

    @Test
    fun appPackageNameTest() {
        val packageName = "com.color.soundreocrder"
        AppCardUtils.setAppPackageName(packageName)
        Assert.assertTrue(packageName == AppCardUtils.appPackageName())
    }

    @Test
    fun appCtxTest() {
        val packageName = "com.color.soundreocrder"
        AppCardUtils.setAppPackageName(packageName)
        val ctx = spy(ApplicationProvider.getApplicationContext<Context>()) ?: return
        doReturn(ctx).doCallRealMethod().`when`(ctx).createPackageContext(anyString(), anyInt())
        Assert.assertNotNull(ctx.appCtx())
    }

    @Test
    fun isFastDoubleClickTest() {
        val systemClockMockStatic = Mockito.mockStatic(SystemClock::class.java)
        systemClockMockStatic.`when`<Long> { SystemClock.elapsedRealtime() }.thenReturn(10000)
        Assert.assertFalse(AppCardUtils.isFastDoubleClick())
        Assert.assertTrue(AppCardUtils.isFastDoubleClick())
        systemClockMockStatic.close()
    }
}