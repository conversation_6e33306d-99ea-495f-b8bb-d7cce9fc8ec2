/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardScaleTransitionTest
 Description:
 Version: 1.0
 Date: 2022/11/23
 Author: W9013333(v-zhengt<PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.dragonfly.transition

import android.content.Context
import android.os.Build
import android.view.View
import android.widget.FrameLayout
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.transition.TransitionValues
import com.soundrecorder.dragonfly.transotion.AppCardScaleTransition
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [])
class AppCardScaleTransitionTest {
    @Test
    fun test() {
        val ctx = ApplicationProvider.getApplicationContext<Context>()
        val rootView = FrameLayout(ctx)
        val view = View(ctx).apply {
            id = 100
        }
        rootView.addView(view)
        AppCardScaleTransition(100).apply {
            view.scaleX = 0f
            val start = TransitionValues(view)
            captureStartValues(start)
            view.scaleX = 1f
            val end = TransitionValues(view)
            captureEndValues(end)
            createAnimator(rootView, start, end)
        }
    }
}