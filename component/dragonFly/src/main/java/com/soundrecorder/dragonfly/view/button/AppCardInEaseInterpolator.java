package com.soundrecorder.dragonfly.view.button;

import android.view.animation.PathInterpolator;

public class AppCardInEaseInterpolator extends PathInterpolator {
    private static final float controlX1 = 0f;
    private static final float controlY1 = 0f;
    private static final float controlX2 = 0.1f;
    private static final float controlY2 = 1.0f;

    public AppCardInEaseInterpolator() {
        super(controlX1, controlY1, controlX2, controlY2);
    }
}
