/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardEngineView
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.dragonfly

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.Keep
import com.oplus.smartengine.annotation.CustomEntityTag
import com.oplus.smartenginecustomlib.IEngineView
import com.soundrecorder.dragonfly.bean.AppCardData
import com.soundrecorder.dragonfly.utils.AppCardUtils
import com.soundrecorder.dragonfly.utils.AppCardUtils.doAction
import com.soundrecorder.dragonfly.utils.AppCardUtils.fromJson
import com.soundrecorder.dragonfly.utils.AppCardUtils.log
import com.soundrecorder.dragonfly.utils.AppCardUtils.runBackground
import com.soundrecorder.dragonfly.view.AppCardLayout
import org.json.JSONObject

@CustomEntityTag("AppCardEngineView")
@Suppress("TooGenericExceptionCaught", "unused")
@Keep
class AppCardEngineView : IEngineView() {
    private var cardData: AppCardData? = null

    private var widgetCode = ""

    @SuppressLint("InflateParams")
    override fun createView(context: Context): View {
        return try {
            AppCardLayout(context)
        } catch (e: Exception) {
            e.log()
            return TextView(context)
        }
    }

    override fun customApplyListData(context: Context, view: View, parent: ViewGroup?) {}

    override fun customParseFromListData(context: Context, jsonObject: JSONObject) {}

    override fun customParseFromJson(context: Context, jsonObject: JSONObject) {
        try {
            val data = fromJson(jsonObject.getString("data"), AppCardData::class.java)
            if (data != null) {
                AppCardUtils.setAppPackageName(data.packageName)
                if (widgetCode.isEmpty() && data.widgetCode.isNotEmpty()) {
                    widgetCode = data.widgetCode
                }
            }
            cardData = data
        } catch (e: Exception) {
            e.log()
        }
    }

    override fun setViewParams(context: Context, view: View, parent: ViewGroup?) {
        val data = cardData ?: return
        val appCardLayout = view as? AppCardLayout ?: return
        appCardLayout.refreshData(data)
    }

    override fun onInVisible(view: View?) {
        "onInVisible view = ${view.hashCode()}".log()
        val appCardLayout = view as? AppCardLayout ?: return
        runBackground {
            //卡片通知录音应用
            appCardLayout.context?.doAction("onInVisible", widgetCode)
        }
        appCardLayout.onInVisible()
    }

    override fun onRelease(view: View?) {
        "onRelease view = ${view.hashCode()}".log()
        val appCardLayout = view as? AppCardLayout ?: return
        runBackground {
            //卡片通知录音应用
            appCardLayout.context.doAction("onInVisible", widgetCode)
        }
        appCardLayout.onInVisible()
    }

    override fun onVisible(view: View?) {
        "onVisible view = ${view.hashCode()}".log()
        val appCardLayout = view as? AppCardLayout ?: return
        runBackground {
            //卡片通知录音应用
            appCardLayout.context.doAction("onVisible", widgetCode)
        }
        appCardLayout.onVisible()
    }
}