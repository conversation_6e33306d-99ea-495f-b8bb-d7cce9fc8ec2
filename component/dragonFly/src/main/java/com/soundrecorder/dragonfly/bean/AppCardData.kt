/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardData
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.dragonfly.bean

import android.graphics.Color
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.annotation.Keep

@Keep
@Suppress("unused")
internal data class AppCardData(
    val packageName: String,
    val widgetCode: String,
    val recordState: Int,
    val saveFileState: Int,
    val timeText: String,
    @ColorInt val timeTextColor: Int,
    val isFakeBoldText: Boolean = true,
    val timeDes: String,
    val stateText: String,
    val recorderName: String,
    @ColorInt val stateTextColor: Int,
    val fileName: String,
    var markSrc: Int,
    @DrawableRes val saveFileSrc: Int,
    @DrawableRes val recordStateSrc: Int,
    @DrawableRes val markRippleSrc: Int,
    @DrawableRes val stateRippleSrc: Int,
    @DrawableRes val saveRippleSrc: Int,
    @DrawableRes val ampsSize: Int,
    val lastAmps: List<Int> = emptyList(),
    @ColorInt val cardColor: Int = Color.parseColor("#FFFAFAFA"),
    @ColorInt var cardWaveColor: Int = Color.parseColor("#D9000000"),
    @ColorInt var cardDashWaveColor: Int = Color.parseColor("#D9666666"),
    val markEnabled: Boolean = true,
    val switchEnabled: Boolean = true,
    val saveEnabled: Boolean = true,
    val hasRefreshTime70: Boolean = false,
    val markDesc: String? = "",
    val recordStateDesc: String? = "",
    val saveDesc: String? = "",
)

@Suppress("unused")
object RecorderState {
    const val INIT = 0
    const val RECORDING = 1
    const val PAUSED = 2
}

@Suppress("unused")
object SaveFileState {
    const val INIT = 0
    const val START_LOADING = 1
    const val SHOW_DIALOG = 2
    const val SUCCESS = 3
    const val ERROR = 4
}


object ClickAction {
    const val CARD_SWITCH_RECORDER_STATUS = "switch_recorder_status"
    const val CARD_ADD_TEXT_MARK = "add_text_mark"
    const val CARD_SAVE_RECORDER_FILE = "save_recorder_file"
    const val CHECK_RECORDER_PERMISSION = "check_recorder_permission"
    const val CHECK_START_SERVICE = "check_start_service"
}

object ActivityAction {
    const val KEY_DO_ACTION = "do_action"
    const val KEY_FILE_NAME = "file_name"
    const val ACTION_SHOW_NO_PERMISSION = "action_show_no_permission"
    const val ACTION_SHOW_SAVE_FILE_SUCCESS = "action_show_save_file_success"
}

@Suppress("unused")
object CheckStartService {
    const val UN_ENABLE_CALL = -1
    const val ENABLE = 0
}
