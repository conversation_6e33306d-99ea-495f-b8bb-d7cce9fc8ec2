package com.soundrecorder.wavemark.mark;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.core.view.ViewKt;
import androidx.recyclerview.widget.AsyncListDiffer;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.coui.appcompat.dialog.COUIAlertDialogBuilder;
import com.coui.appcompat.poplist.COUIPopupListWindow;
import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.shape.ShapeAppearanceModel;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.ClickUtils;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.TimeUtils;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.transition.RemoveItemAnimator;
import com.soundrecorder.common.utils.ViewUtils;
import com.soundrecorder.common.widget.ClickScaleCardView;
import com.soundrecorder.imageload.ImageLoaderUtils;
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudTipManagerAction;
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerAction;
import com.soundrecorder.wavemark.R;
import com.soundrecorder.wavemark.mark.dialog.ReMarkNameAlertDialog;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import static com.soundrecorder.wavemark.mark.pop.MarkMenuPopWindowKt.showListPop;
import kotlin.Unit;

public class MarkListAdapter extends RecyclerView.Adapter<MarkListAdapter.MyViewHolder> {
    private static final String TAG = "MarkListAdapter";
    public MarkDataBean mRenameMark = null;
    public MarkDataBean mDeleteMark = null;
    private final Context mContext;
    private final boolean mCanDelete;
    private final boolean mCanRename;
    private final boolean mNeedAnim;

    private final boolean mHasCheckCloud;
    private RemoveItemAnimator mAnimator = null;

    private ReMarkNameAlertDialog mRenameMarkDialog;
    private AlertDialog mDeleteMarkDialog;
    private COUIPopupListWindow mMenuPop;
    private OnDeleteListener mOnDeleteListener;
    private OnRenameMarkListener mOnRenameMarkListener;
    private OnMarkClickListener mOnMarkClickListener;
    private RecyclerView recyclerView = null;
    private OnShouldShowMenuPopListener mOnShouldShowMenuPopListener = null;

    @Override
    public void onAttachedToRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        this.recyclerView = recyclerView;
        if (mNeedAnim) {
            mAnimator = new RemoveItemAnimator();
            mAnimator.setAddDuration(317);
            mAnimator.setRemoveDuration(233);
            mAnimator.setChangeDuration(383);
            mAnimator.setMoveDuration(383);
        }
        recyclerView.setItemAnimator(mAnimator);
    }

    public MarkListAdapter(Context context, Boolean hasCheckCloud) {
        this(context, hasCheckCloud, true);
    }

    public MarkListAdapter(Context context, Boolean hasCheckCloud, boolean needAnim) {
        this(context, hasCheckCloud, needAnim, true, true);
    }

    public MarkListAdapter(Context context, Boolean hasCheckCloud, boolean needAnim, boolean canDelete, boolean canRename) {
        this.mContext = context;
        this.mNeedAnim = needAnim;
        this.mCanDelete = canDelete;
        this.mCanRename = canRename;
        this.mHasCheckCloud = hasCheckCloud;
    }

    public void setData(List<MarkDataBean> markData) {
        setData(markData, null);
    }

    public void setData(List<MarkDataBean> markData, Runnable commitCallback) {
        try {
            List<MarkDataBean> newMarkData = (markData == null) ? new ArrayList<>() : new ArrayList<>(markData);
            diff.submitList(newMarkData, commitCallback);
        } catch (Exception e) {
            DebugUtil.e(TAG, "setData error", e);
        }
    }

    public void setShowAnimatorPos(int pos) {
        if (mAnimator != null) {
            mAnimator.setShowAnimatorPos(pos);
        }
    }

    @Override
    public int getItemCount() {
        return diff.getCurrentList().size();
    }

    @NotNull
    @Override
    public MyViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new MyViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_mark_list, viewGroup, false), mCanDelete);
    }

    @Override
    public void onViewRecycled(@NonNull MyViewHolder holder) {
        super.onViewRecycled(holder);
        if (holder.mPhotoMark != null) {
            holder.mPhotoMark.setImageBitmap(null);
            holder.mPhotoMark.setImageDrawable(null);
        }
        if (holder.mMarkItemLayout != null) {
            holder.mMarkItemLayout.onRelease();
        }
    }

    @Override
    public void onBindViewHolder(MyViewHolder myViewHolder, int position) {
        //先重置ViewHolder动画状态
        ViewUtils.resetAnimator(myViewHolder.itemView);
        final MarkDataBean markDataBean = diff.getCurrentList().get(position);
        markDataBean.setUpdate(false);
        myViewHolder.setData(markDataBean);
        myViewHolder.mTextMarkTime.setText(markDataBean.getShowTime());
        setContentDiscriptForTimeDuration(myViewHolder.mTextMarkTime, markDataBean.getCorrectTime());
        myViewHolder.mTextMark.setText(getShowMarkText(markDataBean));
        setContentDiscriptForPlayButton(myViewHolder.mTextMark);
        if (markDataBean.fileExists()) {
            myViewHolder.mPhotoMark.setVisibility(View.VISIBLE);
            ImageLoaderUtils.into(myViewHolder.mPhotoMark, markDataBean.getMarkListImageLoadData());
        } else {
            myViewHolder.mPhotoMark.setVisibility(View.GONE);
        }
        if (!mCanDelete && !mCanRename) {
            myViewHolder.mBtnMenuMore.setVisibility(View.GONE);
        } else {
            myViewHolder.mBtnMenuMore.setVisibility(View.VISIBLE);
            myViewHolder.mBtnMenuMore.setOnClickListener(view -> {
                if (!ClickUtils.isFastDoubleClick(ClickUtils.DURATION_300)) {
                    showMenuPop(markDataBean, view);
                }
            });
        }
        myViewHolder.mPhotoMark.setOnClickListener(view -> {
            List<MarkDataBean> pictures = new ArrayList<>();
            diff.getCurrentList().forEach(mark -> {
                if (mark.fileExists()) {
                    pictures.add(mark);
                }
            });
            int index = pictures.indexOf(markDataBean);
            PhotoViewerAction.startWithBigImage(mContext, pictures, index, myViewHolder.mPhotoMark, m -> {
                if (recyclerView != null && m != null) {
                    int childCount = recyclerView.getChildCount();
                    for (int i = 0; i < childCount; i++) {
                        RecyclerView.ViewHolder viewHolder = recyclerView.getChildViewHolder(recyclerView.getChildAt(i));
                        if (viewHolder instanceof MyViewHolder) {
                            MyViewHolder vh = ((MyViewHolder) viewHolder);
                            if (vh.data == m) {
                                PhotoViewerAction.updateImageView(vh.mPhotoMark);
                                return null;
                            }
                        }
                    }
                }
                PhotoViewerAction.updateImageView(null);
                return null;
            });
        });

        myViewHolder.mRootLayout.setOnClickListener(view -> {
            if (mOnMarkClickListener != null) {
                mOnMarkClickListener.onMarkClick(markDataBean);
            }
        });
    }

    private void showMenuPop(MarkDataBean markDataBean, View anchor) {
        dismissMenuPop();
        if (mOnShouldShowMenuPopListener != null && !mOnShouldShowMenuPopListener.shouldShow()) return;
        int[] itemList;
        if (mCanRename && mCanDelete) {
            itemList = new int[]{R.string.rename, R.string.delete};
        } else if (mCanRename) {
            itemList = new int[]{R.string.rename};
        } else {
            itemList = new int[]{R.string.delete};
        }
        mMenuPop = showListPop(anchor, itemList, index -> {
            if (ClickUtils.isFastDoubleClick(ClickUtils.DURATION_300)) return Unit.INSTANCE;
            if (index == R.string.rename) {
                showRenameMark(anchor.getContext(), markDataBean, getShowMarkText(markDataBean));
            } else {
                showDeleteMark(anchor.getContext(), markDataBean);
            }
            dismissMenuPop();
            return Unit.INSTANCE;
        });
    }

    private String getShowMarkText(MarkDataBean markDataBean) {
        if (markDataBean.getMarkText().isEmpty()) {
            if (markDataBean.getDefaultNo() > 0) {
                return BaseApplication.getAppContext().getResources().getString(R.string.default_flag_new, markDataBean.getDefaultNo());
            } else {
                return BaseApplication.getAppContext().getString(R.string.custom_mark_description);
            }
        } else {
            return markDataBean.getMarkText();
        }
    }

    public void showRenameMark(Context context, MarkDataBean data, String newMarkText) {
        if (context == null || data == null) {
            return;
        }
        dismissDeleteDialog();
        dismissRenameDialog();
        mRenameMark = data;
        mRenameMarkDialog = new ReMarkNameAlertDialog((Activity) context, newMarkText, newText -> {
            mOnRenameMarkListener.onRenameMark(data, newText);
            return Unit.INSTANCE;
        });
        mRenameMarkDialog.show();
    }

    @SuppressLint("PrivateResource")
    public void showDeleteMark(Context context, MarkDataBean data) {
        if (context == null || data == null) {
            return;
        }
        dismissDeleteDialog();
        dismissRenameDialog();
        mDeleteMark = data;
        String message;
        if (mHasCheckCloud && CloudTipManagerAction.isCloudSwitchOn()) {
            message = context.getResources().getString(R.string.the_mark_will_be_deleted_from_device_or_cloud);
        } else {
            message = context.getResources().getString(R.string.the_mark_will_be_deleted_from_device);
        }
        COUIAlertDialogBuilder builder = new COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom);
        builder.setBlurBackgroundDrawable(true);
        builder.setTitle(context.getResources().getString(R.string.mark_delete_title));
        builder.setMessage(message);
        builder.setPositiveButton(R.string.delete, (dialog, which) -> {
            if (mOnDeleteListener != null) {
                mOnDeleteListener.onDeleteClick(data);
            }
        });
        builder.setNegativeButton(R.string.cancel, null);
        mDeleteMarkDialog = builder.show();
        ViewUtils.updateWindowLayoutParams(mDeleteMarkDialog.getWindow());
    }

    public ReMarkNameAlertDialog getRenameMarkDialog() {
        return mRenameMarkDialog;
    }

    public AlertDialog getDeleteMarkDialog() {
        return mDeleteMarkDialog;
    }

    private final AsyncListDiffer<MarkDataBean> diff = new AsyncListDiffer<>(this, new DiffUtil.ItemCallback<MarkDataBean>() {

        @Override
        public boolean areItemsTheSame(@NonNull MarkDataBean oldItem, @NonNull MarkDataBean newItem) {
            return oldItem.getTimeInMills() == newItem.getTimeInMills();
        }

        @Override
        public boolean areContentsTheSame(@NonNull MarkDataBean oldItem, @NonNull MarkDataBean newItem) {
            if (newItem.isUpdate()) {
                return false;
            }
            if (oldItem.getCorrectTime() != newItem.getCorrectTime()) {
                return false;
            }
            if (!oldItem.getMarkText().equals(newItem.getMarkText())) {
                return false;
            }
            return oldItem.getPictureFilePath().equals(newItem.getPictureFilePath());
        }
    });

    public void setOnShouldShowMenuPopListener(OnShouldShowMenuPopListener onShouldShowMenuPopListener) {
        this.mOnShouldShowMenuPopListener = onShouldShowMenuPopListener;
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        public View mRootLayout;
        public TextView mTextMark;
        public TextView mTextMarkTime;
        public ImageButton mBtnMenuMore;
        public ShapeableImageView mPhotoMark;
        public ClickScaleCardView mMarkItemLayout;
        public MarkDataBean data;

        public void setData(MarkDataBean data) {
            this.data = data;
        }

        public MyViewHolder(@NonNull @NotNull View itemView, boolean canDelete) {
            super(itemView);
            mRootLayout = itemView.findViewById(R.id.root_layout);
            mTextMark = itemView.findViewById(R.id.text_mark_description);
            mTextMarkTime = itemView.findViewById(R.id.text_mark_time);
            mBtnMenuMore = itemView.findViewById(R.id.btnMenuMore);
            mPhotoMark = itemView.findViewById(R.id.img_mark_photo);
            mMarkItemLayout = itemView.findViewById(R.id.mark_item_layout);
            mPhotoMark.setShapeAppearanceModel(new ShapeAppearanceModel.Builder().setAllCornerSizes(ViewUtils.dp2px(4f, true)).build());
            mPhotoMark.setAccessibilityDelegate(new View.AccessibilityDelegate() {
                @Override
                public void onInitializeAccessibilityNodeInfo(View host, AccessibilityNodeInfo info) {
                    info.setClassName(Button.class.getName());
                    super.onInitializeAccessibilityNodeInfo(host, info);
                }
            });
        }
    }

    public void setOnDeleteListener(OnDeleteListener onDeleteListener) {
        mOnDeleteListener = onDeleteListener;
    }

    public void setOnRenameMarkListener(OnRenameMarkListener onRenameMarkListener) {
        mOnRenameMarkListener = onRenameMarkListener;
    }

    public void setOnMarkClickListener(OnMarkClickListener onMarkClickListener) {
        mOnMarkClickListener = onMarkClickListener;
    }

    public interface OnDeleteListener {
        void onDeleteClick(MarkDataBean data);
    }

    public interface OnMarkClickListener {
        void onMarkClick(MarkDataBean data);
    }

    public interface OnRenameMarkListener {
        void onRenameMark(MarkDataBean data, String newMarkText);
    }

    private void setContentDiscriptForTimeDuration(View v, long timeDuration) {
        String timeStringForDuration = TimeUtils.getFormatContentDescriptionTimeByMillisecond(mContext, timeDuration);
        v.setContentDescription(timeStringForDuration);
        v.setAccessibilityDelegate(new View.AccessibilityDelegate() {
            @Override
            public void onInitializeAccessibilityNodeInfo(View view, AccessibilityNodeInfo accessibilityNodeInfo) {
                super.onInitializeAccessibilityNodeInfo(view, accessibilityNodeInfo);
                accessibilityNodeInfo.setClassName(Button.class.getName());
            }
        });

    }

    private void setContentDiscriptForPlayButton(View markView) {
        markView.setAccessibilityDelegate(new View.AccessibilityDelegate() {
            @Override
            public void sendAccessibilityEvent(View host, int eventType) {
                super.sendAccessibilityEvent(host, eventType);
            }

            @Override
            public void onInitializeAccessibilityNodeInfo(View view, AccessibilityNodeInfo accessibilityNodeInfo) {
                super.onInitializeAccessibilityNodeInfo(view, accessibilityNodeInfo);
                accessibilityNodeInfo.setClassName(Button.class.getName());
            }
        });
    }

    public boolean isRenameMarkDialogShowing() {
        return (mRenameMarkDialog != null) && (mRenameMarkDialog.isShowing());
    }

    public boolean isDeleteMarkDialogShowing() {
        return (mDeleteMarkDialog != null) && (mDeleteMarkDialog.isShowing());
    }

    public void dismissRenameDialog() {
        if ((mRenameMarkDialog != null) && (mRenameMarkDialog.isShowing())) {
            mRenameMarkDialog.dismiss();
        }
        mRenameMarkDialog = null;
    }

    public void dismissDeleteDialog() {
        if ((mDeleteMarkDialog != null) && (mDeleteMarkDialog.isShowing())) {
            mDeleteMarkDialog.dismiss();
        }
        mDeleteMarkDialog = null;
    }

    public void dismissMenuPop() {
        if (mMenuPop != null && mMenuPop.isShowing()) {
            mMenuPop.dismiss();
        }
        mMenuPop = null;
    }
}
