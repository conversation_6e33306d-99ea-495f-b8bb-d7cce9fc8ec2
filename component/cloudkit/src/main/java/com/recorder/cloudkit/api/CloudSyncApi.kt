/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/4/30
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.recorder.cloudkit.api

import android.app.Activity
import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.recorder.cloudkit.SyncTriggerManager
import com.recorder.cloudkit.operation.CloudConfigManager
import com.recorder.cloudkit.account.AccountManager
import com.recorder.cloudkit.account.AccountPref
import com.recorder.cloudkit.account.VerifyConfirmHelper
import com.recorder.cloudkit.global.CloudGlobalStateManager
import com.recorder.cloudkit.push.CloudPushAgent
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.CloudSyncAgent
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.modulerouter.RecordMediaCompareAction
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction.VerifyCallBack


@Component(CloudSyncAction.COMPONENT_NAME)
object CloudSyncApi {
    private const val TAG = "CloudSyncApi"


    /**
     * 设置同步过程中是否忽略高温条件
     * true：检测到高温，也不会停止同步
     * false：检测到高温，停止同步
     */
    @Action(CloudSyncAction.ACTION_IGNORE_HIGH_TEMPERATURE)
    @JvmStatic
    fun setIgnoreHighTemperature(ignore: Boolean) {
        DebugUtil.i(TAG, "setIgnoreHighTemperature $ignore")
        CloudSyncAgent.getInstance().ignoreCheckHighTemperature = ignore
    }

    /**
     * 是否支持云同步
     */
    @Action(CloudSyncAction.ACTION_REGION_SUPPORT_CLOUD)
    @JvmStatic
    fun isSupportCloudArea(): Boolean {
        return CloudSynStateHelper.isRegionCloudSupport() && CloudGlobalStateManager.canShowSyncSwitch()
    }

    @Action(CloudSyncAction.ACTION_QUERY_CLOUD_SWITCH_STATE)
    @JvmStatic
    fun queryCloudSwitchState(checkLogin: Boolean = true): Int {
        return CloudSynStateHelper.getSwitchState(checkLogin)
    }

    /**
     * 注册push
     */
    @Action(CloudSyncAction.ACTION_REGISTER_PUSH_IF_NEED)
    @JvmStatic
    fun registerPushIfNeed(context: Context) {
        CloudPushAgent.checkPushRegister(context)
    }

    /**
     * 停止当前同步且清楚锚点
     */
    @Action(CloudSyncAction.STOP_SYNC_FOR_CLEAR_VERSION)
    @JvmStatic
    fun stopSyncForClearAnchor(context: Context) {
        DebugUtil.i(TAG, "stopSyncForClearAnchor")
        SyncTriggerManager.getInstance(context).trigStopSyncForClearAnchor()
    }


    /**
     * 触发全量同步后触发云同步
     */
    @Action(CloudSyncAction.TRIG_MEDIA_DB_SYNC)
    @JvmStatic
    fun trigMediaDBSync(context: Context, syncType: Int) {
        DebugUtil.i(TAG, "trigMediaDBSync  $syncType, isCloudOn ${TipStatusManager.isCloudOn()}", true)
        if (queryCloudSwitchState(false) <= CloudSwitchState.CLOSE) {
            // 云同步未打开，只走全量
            SyncTriggerManager.getInstance(context).scheduleWorkerJobWithoutCheckCloud({
                RecordMediaCompareAction.doMediaCompare(false, syncType)
            }, 0)
            return
        }
        when (syncType) {
            CloudSyncAction.SYNC_TYPE_RECOVERY_START_APP ->
                SyncTriggerManager.getInstance(context).trigRecoveryNow(true, SyncTriggerManager.RECOVERY_FROM_START_APP)
            CloudSyncAction.SYNC_TYPE_RECOVERY_MANUAL ->
                SyncTriggerManager.getInstance(context).trigRecoveryNow(true, SyncTriggerManager.RECOVERY_FROM_MANUAL)
            CloudSyncAction.SYNC_TYPE_BACKUP ->
                SyncTriggerManager.getInstance(context).trigBackupNow()
            else ->
                DebugUtil.e(TAG, "trigMediaDBSyncDelayed not support syncType $syncType, true")
        }
    }

    /**
     * 触发云同步或云备份
     * @param syncType @see SYNC_TYPE_BACKUP,SYNC_TYPE_RECOVERY_MANUAL,SYNC_TYPE_RECOVERY_START_APP
     */
    @Action(CloudSyncAction.TRIG_CLOUD_SYNC)
    @JvmStatic
    fun trigCloudSync(context: Context, syncType: Int) {
        DebugUtil.i(TAG, "trigCloudSync, syncType = $syncType", true)
        val syncTriggerManager = SyncTriggerManager.getInstance(context)
        when (syncType) {
            CloudSyncAction.SYNC_TYPE_BACKUP -> syncTriggerManager.trigBackupNow()
            CloudSyncAction.SYNC_TYPE_RECOVERY_START_APP -> syncTriggerManager.trigRecoveryNow(SyncTriggerManager.RECOVERY_FROM_START_APP)
            CloudSyncAction.SYNC_TYPE_RECOVERY_MANUAL -> syncTriggerManager.trigRecoveryNow(SyncTriggerManager.RECOVERY_FROM_MANUAL)
            else -> DebugUtil.e(TAG, "not support syncType = $syncType")
        }
    }


    /**
     * 触发备份
     */
    @Action(CloudSyncAction.TRIG_BACK_UP)
    @JvmStatic
    fun trigBackupNow(context: Context) {
        DebugUtil.i(TAG, "trigBackupNow", true)
        trigCloudSync(context, CloudSyncAction.SYNC_TYPE_BACKUP)
    }

    /**
     *  停止当前同步流程，且会清空同步锚点
     *  @param context
     *  @param deleteData 是否删除本地已同步数据
     *  true: 删除已同步过记录的相关音频、转文本等相关信息
     *  false： 删除已同步记录的 云端id以及同步状态信息
     */
    @Action(CloudSyncAction.STOP_SYNC_FOR_LOGIN_OUT)
    @JvmStatic
    fun stopSyncForLoginOut(context: Context, deleteData: Boolean) {
        SyncTriggerManager.getInstance(context).trigStopSyncForLoginOut(deleteData)
    }

    @Action(CloudSyncAction.RELEASE_CLOUD_SYNC)
    @JvmStatic
    fun release() {
        DebugUtil.i(TAG, "release", true)
        SyncTriggerManager.release()
        TipStatusManager.release()
        /*未使用录音，解注册push，不拉起*/
        CloudPushAgent.unregister()
        /*重置一体化开关状态*/
        CloudGlobalStateManager.reset()
    }

    /**
     * 获取是否支持开关版本
     */
    @Action(CloudSyncAction.ACTION_SUPPORT_SWITCH)
    @JvmStatic
    fun isSupportSwitch(): Boolean {
        DebugUtil.i(TAG, "isSupportSwitch")
        return CloudSynStateHelper.isSupportSwitch()
    }

    /**
     * 设置开关状态
     */
    @Action(CloudSyncAction.ACTION_SET_SYNC_SWITCH)
    @JvmStatic
    fun setSyncSwitch(state: Int, report: Boolean = true): Boolean {
        DebugUtil.i(TAG, "setSyncSwitch")
        return CloudSynStateHelper.setSyncSwitch(state, report)?.bizErrorCode == 0
    }

    @Action(CloudSyncAction.ACTION_SCHEDULE_SYNC_RUNNABLE)
    @JvmStatic
    fun scheduleSyncRunnable(context: Context, runnable: Runnable, delayTime: Long): Boolean {
        DebugUtil.i(TAG, "scheduleSyncRunnable")
        return SyncTriggerManager.getInstance(context).scheduleWorkerJob(runnable, delayTime)
    }

    @Action(CloudSyncAction.ACTION_IS_CLOUD_ON)
    @JvmStatic
    fun isCloudOn(): Boolean {
        return TipStatusManager.isCloudOn()
    }

    /**
     * 获取云服务 顶部提示配置
     */
    @Action(CloudSyncAction.ACTION_CLOUD_SETTINGS)
    @JvmStatic
    fun getCloudSettings(dataStr: MutableLiveData<String>) {
        return CloudConfigManager.getOperationData(dataStr)
    }

    @Action(CloudSyncAction.ACTION_CLEAR_LAST_USER_DATA)
    @JvmStatic
    fun clearLastUserData(context: Context) {
        // 当前登录账号同SP中存的非空值不相同，执行一次关闭开关操作，更新Sp中的值
        if (checkUserIdChanged(context)) {
            CloudSyncRecorderDbUtil.clearLocalSyncStatusForLogingout(context)
            CloudSyncManager.getInstance().clearUserDataOnLogout()
            AccountPref.setAccountUId(context, AccountManager.getInstance().getLoginIdFromCache(context))
        }
    }

    /**
     * 验证SP中的用户不为null，且跟当前登录用户不一致
     */
    @JvmStatic
    private fun checkUserIdChanged(context: Context): Boolean {
        val oldId = AccountPref.getAccountUId(context)
        return (!oldId.isNullOrBlank()) && (oldId != AccountManager.getInstance().getLoginIdFromCache(context))
    }

    /**
     * 云同步SDK二次校验
     */
    @Action(CloudSyncAction.ACTION_CHECK_LOGIN_AND_VERIFY)
    @JvmStatic
    fun checkLoginAndVerify(activity: Activity, callback: VerifyCallBack) {
        VerifyConfirmHelper.checkLoginAndVerify(activity, callback)
    }

    /**
     * 云同步SDK二次校验
     */
    @Action(CloudSyncAction.ACTION_CHECK_IS_ACCOUNT_VERIFY)
    @JvmStatic
    fun checkAccountIsVerified(): Boolean {
        return AccountManager.getInstance().checkAccountIsNeedVerify()
    }
}