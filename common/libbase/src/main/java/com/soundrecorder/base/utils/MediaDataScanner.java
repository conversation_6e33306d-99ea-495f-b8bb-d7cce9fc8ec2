
/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: MediaDataScanner
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/
package com.soundrecorder.base.utils;

import android.content.Context;
import android.media.MediaScannerConnection;

import java.util.ArrayList;

public class MediaDataScanner {

    private static final String TAG = "MediaDataScanner";
    private static final int THRESHOLD_FLUSH = 50;
    private static final int THRESHOLD_FLUSH_FIRST_TIME = 10;

    private static boolean sFirstBatchFlush = false;
    private static volatile MediaDataScanner sInstance;

    private final Object mLock = new Object();
    private ArrayList<String> mPaths = new ArrayList<>();

    public static synchronized MediaDataScanner getInstance() {
        if (sInstance == null) {
            sInstance = new MediaDataScanner();
        }
        return sInstance;
    }


    public void add(Context context, Object path) {
        synchronized (mLock) {
            mPaths.add((String) path);
            if (sFirstBatchFlush) {
                if (mPaths.size() >= THRESHOLD_FLUSH_FIRST_TIME) {
                    doFlush(context);
                }
            } else {
                if (mPaths.size() >= THRESHOLD_FLUSH) {
                    doFlush(context);
                }
            }
        }
    }

    public void flush(Context context) {
        synchronized (mLock) {
            doFlush(context);
        }
    }

    private void doFlush(Context context) {
        mediaScan(context, mPaths);
        mPaths.clear();
        sFirstBatchFlush = false;
    }

    public void mediaScan(Context context, ArrayList<String> paths) {
        if ((paths == null) || (paths.size() <= 0)) {
            return;
        }
        String[] pathList = new String[paths.size()];
        paths.toArray(pathList);
        mediaScan(context, pathList);
    }

    public void mediaScan(Context context, String[] paths) {
        mediaScanWithCallback(context, paths, null);
    }

    public void mediaScanWithCallback(Context context, String[] paths, MediaScannerConnection.OnScanCompletedListener mListener) {
        String[] mimeTypes = new String[]{"audio/*"};
        mediaScan(context, paths, mimeTypes, mListener);
    }

    public void mediaScan(Context context, String[] paths, String[] mimeTypes, MediaScannerConnection.OnScanCompletedListener listener) {
        if ((context == null) || (paths == null) || (paths.length <= 0)) {
            return;
        }
        try {
            MediaScannerConnection.scanFile(context, paths, mimeTypes, listener);
        } catch (Exception ignored) {
        }
    }

    public void mediaScanPicture(Context context, ArrayList<String> paths) {
        if ((paths == null) || (paths.size() <= 0)) {
            return;
        }
        String[] pathList = new String[paths.size()];
        paths.toArray(pathList);
        String[] mimeTypes = new String[]{"image/jpeg"};
        mediaScan(context, pathList, mimeTypes, null);
    }
}
