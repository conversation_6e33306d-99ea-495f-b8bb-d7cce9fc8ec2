// OPLUS Java File Skip Rule:TooGenericExceptionCaught
package com.soundrecorder.base;

import static com.soundrecorder.base.ext.ExtKt.isFlexibleWindow;

import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;

import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.ActivityResultRegistry;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityOptionsCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;

import com.coui.appcompat.theme.COUIThemeOverlay;
import com.coui.responsiveui.config.ResponsiveUIConfig;
import com.soundrecorder.base.splitwindow.FoldingWindowObserver;
import com.soundrecorder.base.backpressed.OnBackPressedListener;
import com.soundrecorder.base.splitwindow.OnFragmentFoldStateChangedListener;
import com.soundrecorder.base.userchange.OnFragmentUserChangeListener;
import com.soundrecorder.base.userchange.UserChangeManager;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FeatureOption;
import com.soundrecorder.base.utils.ScreenUtil;
import com.soundrecorder.base.utils.StatusBarUtil;
import com.soundrecorder.modulerouter.CommonAction;
import org.jetbrains.annotations.NotNull;
import java.util.List;

public class BaseActivity extends AppCompatActivity {
    private static final String TAG = "BaseActivity";
    private FoldingWindowObserver mFoldingWindowObserver = null;
    private final Observer<Boolean> mObserver = (Observer<Boolean>) aBoolean -> {
        if (aBoolean) {
            DebugUtil.d(TAG, getFunctionName() + " User Change Success");
            userChange();
        }
    };

    protected ActivityResultRegistry createActivityResultRegistry = new ActivityResultRegistry() {
        @Override
        public <I, O> void onLaunch(int requestCode, @NonNull ActivityResultContract<I, O> contract, I input, @Nullable ActivityOptionsCompat options) {
            onActivityResultLaunch(requestCode);
            getActivityResultRegistry().onLaunch(requestCode, contract, input, options);
        }
    };

    protected void onActivityResultLaunch(int requestCode) {}


    /**
     * 重定义ActivityResultRegistry，使用registerForActivityResult又重建问题，请使用这个方法代替
     */
    public <I, O> ActivityResultLauncher<I> registerForActivityResultByCustom(
            ActivityResultContract<I, O> contract,
            ActivityResultCallback<O> callback
    ) {
        return registerForActivityResult(contract, createActivityResultRegistry, callback);
    }

    @ColorRes
    public int navigationBarColor() {
        return R.color.common_background_color;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        COUIThemeOverlay.getInstance().applyThemeOverlays(this);
        setRequestedOrientation();
        setTheme(R.style.DarkForceTheme);
        super.onCreate(savedInstanceState);
        DebugUtil.e(TAG, "baseActivity onCreate clear and set default display");
        StatusBarUtil.INSTANCE.setStatusBarTransparentAndBlackFont(this, navigationBarColor());
        UserChangeManager.initObserveForever(mObserver);
    }

    @Override
    public void onConfigurationChanged(@NonNull @NotNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        ResponsiveUIConfig.getDefault(this).onActivityConfigChanged(newConfig);
        setRequestedOrientation();
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        try {
            return super.dispatchKeyEvent(event);
        } catch (Exception e) {
            DebugUtil.w(TAG, "dispatchKeyEvent error: " + e.toString());
            return false;
        }
    }

    protected void setRequestedOrientation() {
        if (FeatureOption.IS_PAD || ScreenUtil.INSTANCE.isUnFoldStatusWithMultiWindow(this)) {
            if (FeatureOption.isHasSupportDragonfly()) {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_NOSENSOR);
            } else {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_USER);
            }
        } else {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_NOSENSOR);
        }
    }

    @Override
    public void onBackPressed() {
        if (!processBackPress()) {
            super.onBackPressed();
        }
    }

    protected boolean processBackPress() {
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        for (int i = fragments.size() - 1; i >= 0; i--) {
            Fragment fragment = fragments.get(i);
            if (fragment instanceof OnBackPressedListener) {
                OnBackPressedListener backFragment = (OnBackPressedListener) fragment;
                if (backFragment.onBackPressed()) {
                    return true;
                }
            }
        }
        return false;
    }

    protected void fragmentStateChanged(int state) {
        //如果后期多个fragment共存，存在同时后台刷的情况
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        for (int i = fragments.size() - 1; i >= 0; i--) {
            Fragment fragment = fragments.get(i);
            if (fragment instanceof OnFragmentFoldStateChangedListener) {
                OnFragmentFoldStateChangedListener listener = (OnFragmentFoldStateChangedListener) fragment;
                listener.onFoldStateChanged(state);
            }
        }
    }

    protected void fragmentUserChange() {
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        for (int i = fragments.size() - 1; i >= 0; i--) {
            Fragment fragment = fragments.get(i);
            if (fragment instanceof OnFragmentUserChangeListener) {
                OnFragmentUserChangeListener listener = (OnFragmentUserChangeListener) fragment;
                listener.onUserChange();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        for (Fragment fragment : fragments) {
            fragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    protected void onDestroy() {
        mFoldingWindowObserver = null;
        UserChangeManager.removeObserver(mObserver);
        super.onDestroy();
    }

    @Override
    protected void onStart() {
        super.onStart();
        initFoldingWindowObserver();
    }

    private void initFoldingWindowObserver() {
        if (!FeatureOption.IS_SUPPORT_HOVER_MODE) {
            return;
        }
        if (mFoldingWindowObserver == null) {
            mFoldingWindowObserver = new FoldingWindowObserver(this);
            //状态发生改变
            mFoldingWindowObserver.setMOnScreenFoldStateChangeListener(this::onFoldStateChanged);
        }
        getLifecycle().addObserver(mFoldingWindowObserver);
    }

    protected void onFoldStateChanged(int state) {
        DebugUtil.i(TAG, "onFoldStateChanged = " + state);
        String name = getFunctionName();
        if (state == FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER) {
            DebugUtil.i(TAG, "className = " + name);
            if (!TextUtils.isEmpty(name)) {
                CommonAction.hoverBuryingPoint(name);
            }
        }
    }

    protected void userChange() {
        finish();
    }

    protected String getFunctionName() {
        return "";
    }

    @Override
    public void finish() {
        super.finish();
        if (isFlexibleWindow(this)) {
            overridePendingTransition(com.support.appcompat.R.anim.coui_close_slide_enter_noalpha, com.support.appcompat.R.anim.coui_close_slide_exit);
        }
    }
}
