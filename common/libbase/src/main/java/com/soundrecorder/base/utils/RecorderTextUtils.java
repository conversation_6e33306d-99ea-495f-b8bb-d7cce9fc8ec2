package com.soundrecorder.base.utils;

import android.content.Context;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import android.widget.EditText;
import android.widget.Toast;

import com.soundrecorder.base.R;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RecorderTextUtils {
    private static final String EMOJI_REGEX = "？|⭕|⭐|[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[☀-⟿]|[\ud83e\udd10-\ud83e\udd20]";
    private static final String FILE_NAME_DYNAMIC_REGEX = ".*[\\\\/*:?<>|\"]+?.*";
    private static Pattern sEmojiPattern;
    private static Pattern sFileNameIllegalPattern;

    public RecorderTextUtils() {
    }

    public static CharSequence filterEmoji(CharSequence source) {
        if (TextUtils.isEmpty(source)) {
            return source;
        } else {
            if (sEmojiPattern == null) {
                sEmojiPattern = Pattern.compile("？|⭕|⭐|[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[☀-⟿]|[\ud83e\udd10-\ud83e\udd20]");
            }

            Matcher matcher = sEmojiPattern.matcher(source);
            return matcher.replaceAll("");
        }
    }

    public static boolean containsEmoji(CharSequence source) {
        if (TextUtils.isEmpty(source)) {
            return false;
        } else {
            if (sEmojiPattern == null) {
                sEmojiPattern = Pattern.compile("？|⭕|⭐|[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[☀-⟿]|[\ud83e\udd10-\ud83e\udd20]");
            }

            Matcher matcher = sEmojiPattern.matcher(source);
            return matcher.find();
        }
    }

    public static boolean containsIllegalCharFileName(CharSequence source) {
        if (TextUtils.isEmpty(source)) {
            return false;
        } else {
            if (sFileNameIllegalPattern == null) {
                sFileNameIllegalPattern = Pattern.compile(".*[\\\\/*:?<>|\"]+?.*");
            }

            Matcher matcher = sFileNameIllegalPattern.matcher(source);
            return matcher.find();
        }
    }

    public static void addInputFilter(EditText editText, InputFilter inputFilter) {
        InputFilter[] filters = editText.getFilters();
        InputFilter[] tmpFilter = null;
        int length = 0;
        if (filters == null) {
            tmpFilter = new InputFilter[1];
        } else {
            length = filters.length;
            tmpFilter = new InputFilter[length + 1];
        }

        System.arraycopy(filters, 0, tmpFilter, 0, length);
        tmpFilter[length] = inputFilter;
        editText.setFilters(tmpFilter);
    }

    public static void addEmojiInputFilter(EditText editText, final Toast toast) {
        addInputFilter(editText, new InputFilter() {
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (RecorderTextUtils.containsEmoji(source)) {
                    if (toast != null) {
                        toast.show();
                    }

                    if (dest != null) {
                        return dest.subSequence(dstart, dend);
                    }
                }

                return source;
            }
        });
    }

    public static void addIllgalFileNameInputFilter(EditText editText, final Toast toast) {
        addInputFilter(editText, new InputFilter() {
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (RecorderTextUtils.containsIllegalCharFileName(source)) {
                    if (toast != null) {
                        toast.show();
                    }

                    if (dest != null) {
                        return dest.subSequence(dstart, dend);
                    }
                }

                return source;
            }
        });
    }

    public static void addLengthInputFilter(EditText editText, final int length, final Toast toast) {
        addInputFilter(editText, new InputFilter() {
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (TextUtils.isEmpty(source)) {
                    return source;
                } else {
                    int slen = source.length();
                    if (TextUtils.isEmpty(dest) && slen > length) {
                        return source.subSequence(0, length);
                    } else {
                        int dlen = dest.length();
                        int remindLen = length - dlen > 0 ? length - dlen : 0;
                        if (slen > remindLen) {
                            if (toast != null) {
                                toast.show();
                            }

                            return source.subSequence(0, remindLen);
                        } else {
                            return source;
                        }
                    }
                }
            }
        });
    }

    public static String getNewProgressDescription(Context context, long positionMs, long durationMs) {
        long percent = (long) (((float) positionMs) / ((float) durationMs) * NumberConstant.NUM_F100);
        return getNewProgressDescription(context, percent);
    }

    public static String getNewProgressDescription(Context context, long progress) {
        String[] array = new String[4];
        array[0] = context.getString(R.string.talkback_progressbar);
        array[1] = context.getString(R.string.content_description_move_block);
        array[2] = String.format(context.getString(R.string.talkback_progressbar_percent), progress);
        array[3] = context.getString(R.string.talkback_progressbar_can_scroll_user_two_finger);
        return Arrays.toString(array);
    }
}
