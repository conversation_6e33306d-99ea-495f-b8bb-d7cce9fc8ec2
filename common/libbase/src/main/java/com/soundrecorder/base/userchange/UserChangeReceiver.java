/************************************************************
 * Copyright 2000-2019 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : UserChangeReceiver.java
 * Version Number: 1.0
 * Description   :
 * Author        : W9000845
 * Date          : 2019-08-28
 * History       :(ID,  2019-08-28, TianJun, Description)
 ************************************************************/

package com.soundrecorder.base.userchange;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.soundrecorder.base.utils.DebugUtil;

public class UserChangeReceiver extends BroadcastReceiver {

    private String TAG = "UserChangeListener";
    private UserChangeListener mUserChangeListener = null;

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent != null) {
            if (Intent.ACTION_USER_BACKGROUND.equals(intent.getAction())) {
                DebugUtil.i(TAG, "registerUserChangerReceiver action:" + intent.getAction());
                if (mUserChangeListener != null) {
                    mUserChangeListener.doUserChange();
                }
            }
        } else {
            DebugUtil.e(TAG, "registerUserChangerReceiver intent is null.");
        }
    }

    public interface UserChangeListener {
        void doUserChange();
    }

    public void setUserChangeListener(UserChangeListener listener) {
        mUserChangeListener = listener;
    }
}
