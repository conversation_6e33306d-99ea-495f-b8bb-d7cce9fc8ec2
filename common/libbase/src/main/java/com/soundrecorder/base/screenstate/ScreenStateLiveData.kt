/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ScreenStateLiveData
 * * Description : 屏幕锁屏变更liveData
 * * Version     : 1.0
 * * Date        : 2022/8/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.screenstate

import android.content.Context
import android.hardware.display.DisplayManager
import android.view.Display
import androidx.lifecycle.LiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil

open class ScreenStateLiveData : LiveData<Boolean>() {

    companion object {
        private const val TAG = "ScreenStateEvent"
    }

    private var mDisplayManager = BaseApplication.getAppContext().getSystemService(Context.DISPLAY_SERVICE) as DisplayManager

    private var mDisplayListener: DisplayManager.DisplayListener =
        object : DisplayManager.DisplayListener {
            override fun onDisplayAdded(displayId: Int) {
                //do nothing
            }
            override fun onDisplayRemoved(displayId: Int) {
                //do nothing
            }
            override fun onDisplayChanged(displayId: Int) {
                updateScreenState()
            }
        }

    private fun updateScreenState() {
        /* Note that we don't listen to Intent.SCREEN_ON and Intent.SCREEN_OFF because they are no
         * longer adequate for monitoring the screen state since they are not sent in cases where
         * the screen is turned off transiently such as due to the proximity sensor.
         */
        val displays = mDisplayManager.displays
        if (displays != null) {
            for (display in displays) {
                /*
                 * Anything other than STATE_ON is treated as screen off, such as STATE_DOZE,
                 * STATE_DOZE_SUSPEND, etc...
                 */
                if (display.state == Display.STATE_ON) {
                    DebugUtil.d(TAG, "Screen " + display.state + " on")
                    setDisplayValue(true)
                    return
                }
            }
            DebugUtil.d(TAG, "Screens all off")
        }
        DebugUtil.d(TAG, "No displays found")
        setDisplayValue(false)
    }

    private fun setDisplayValue(newValue: Boolean) {
        if (value != newValue) {
            postValue(newValue)
        }
    }

    fun getNewValue(): Boolean? {
        if (value == null) {
            updateScreenState()
        }

        return value
    }

    override fun onActive() {
        mDisplayManager.registerDisplayListener(mDisplayListener, null)
    }

    override fun onInactive() {
        mDisplayManager.unregisterDisplayListener(mDisplayListener)
    }
}