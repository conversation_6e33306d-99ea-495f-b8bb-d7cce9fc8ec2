package com.soundrecorder.base;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.res.Configuration;
import android.text.TextUtils;
import android.view.View;

import com.inno.ostitch.OStitch;
import com.oplus.recorderlog.log.RecorderLogger;
import com.soundrecorder.base.arms.AppDelegate;
import com.soundrecorder.base.utils.FeatureOption;
import com.soundrecorder.base.utils.MultiUserUtils;
import com.soundrecorder.base.utils.OpenIdUtils;
import com.soundrecorder.modulerouter.summary.SummaryAction;
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction;

import java.util.Locale;

public class BaseApplication extends Application {

    public static boolean sIsRTLanguage = false;
    public static boolean sNeedToNormalRingMode = false;
    public static boolean sIsMainSystem = true;  //true 主系统，false 系统分身
    private static BaseApplication mInstance;
    private AppDelegate mAppDelegate;

    public static void setInstance(BaseApplication mInstance) {
        BaseApplication.mInstance = mInstance;
    }

    public static BaseApplication getApplication() {
        return mInstance;
    }

    public static Context getAppContext() {
        return mInstance.getApplicationContext();
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        setInstance(this);
        if (isMainProcess()) {
            //这里需要在调用日志打印之前启动Ostitch的初始化，不然日志打捞相关的Router不起作用
            OStitch.init(base);
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        if (!isMainProcess()) {
            return;
        }
        onCreateInit();
    }

    protected void onCreateInit() {
        FeatureOption.loadOptions(this);
        initSdks();
        sIsRTLanguage = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL;
        sIsMainSystem = !MultiUserUtils.isSystemClone();
        mAppDelegate = new AppDelegate(this);
        mAppDelegate.onApplicationCreate();
    }

    private void initSdks() {
        Thread thread = new Thread() {
            @Override
            public void run() {
                // 获取是否支持录音摘要能力
                SummaryAction.initSupportSummary(false);
                //初始化OpenIdSdk
                OpenIdUtils.INSTANCE.init(getAppContext());
                // 获取是否支持AI ASR能力
                AIAsrManagerAction.loadSupportAIAsr(getAppContext(), true);
                //初始化XLog sdk
                RecorderLogger.INSTANCE.initLog(getAppContext());
            }
        };
        thread.start();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        sIsRTLanguage = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL;
    }

    public void onDestroyedRelease(Activity activity) {
    }

    public Boolean isMainProcess() {
        try {
            return getPackageName().equals(getProcessName());
        } catch (Exception e) {
            return false;
        }
    }
}
