/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - BounceCallBack.kt
 ** Description:  The event forwarding of the first layer is judged. The user wants to do some
 **               judgment processing before forwarding. For example, the change in the x direction
 **               is greater than the y direction, and it is considered that forwarding is required.
 **
 ** Version: 1.1
 ** Date: 2019-04-30
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2019-04-30   1.1         Convert this demo into Kotlin
 ********************************************************************************/

package com.soundrecorder.base.refresh

interface EventForwardingHelper {
    //Return true to not forward to child
    fun notForwarding(downX: Float, downY: Float, moveX: Float, moveY: Float): Boolean
}