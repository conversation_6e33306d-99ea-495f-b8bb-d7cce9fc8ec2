/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MessageEntryBean
 * Description:
 * Version: 1.0
 * Date: 2022/9/19
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/9/19 1.0 create
 */

package com.soundrecorder.base.splitwindow.bracketspace

data class MessageEntryBean(val messageId: String) {
    /*messageId: 注意：正常来说这个id是固定的，除非需要插入多条数据，才需要变更id;更新数据使用同样的message_id即可*/

    /*应用包名，以便悬停空间根据包名拿到应用图标，图标不需要应用额外提供。*/
    var packageName: String? = null

    /*主标题*/
    var title: String? = null

    /*副标题*/
    var summary: String? = null

    /*内容区,可选*/
    var content: String? = null

    /*悬停空间页面的跳转intent，intent要使用Intent.toUri转化成String类型，适用方在跳转唤醒时会使用此请求转化成intent拉起。
    * 注意使用intent.toUri(0)必须保证Intent.parseUri(uri, 0)可以完整还原原始Intent
    * 应用如果需要在Intent中携带Extra信息需要满足Extra是以下9种类型
    * String|Boolean|Byte|Character|Double|Float|Integer|Long|Short*/
    var targetIntent: String? = null

    /*海报图片uri（pkg_1.jpg/png这种，悬停空间根据这个，找到图片文件）. 海报图片由三方应用调用悬停空间应用的provider写到悬停空间的存储空间，方便做预览展示*/
    var pictureName: String? = null

    /*进度百分比（可选），录音不涉及*/
    var playProgress: Int? = null
}