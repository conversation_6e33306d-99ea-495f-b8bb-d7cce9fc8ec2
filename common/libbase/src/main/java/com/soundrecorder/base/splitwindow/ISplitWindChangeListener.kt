/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FoldingWindowObserver
 * Description:
 * Version: 1.0
 * Date: 2022/7/25
 * Author: wang<PERSON><PERSON>(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * wangweihua 2022/7/25 1.0 create
 */
package com.soundrecorder.base.splitwindow


import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

interface ISplitWindChangeListener {


    fun onWindSplitStatusChangedOrFoldStatusChanged(windowParameter: SplitWindowParameter)

    /**
     * 屏幕相关参数：
     * windowWith 窗口宽度（分屏或未分屏）
     * windowHeight 窗口高度（分屏或未分屏）
     * splitWindow 是否处于分屏模式，ture->分屏状态 false->非分屏状态
     * isUnfold 是否处于展开状态， true->孔雀手机展开状态 false->孔雀手机折叠状态，普通手机
     * isPortrait 是否处于竖屏状态， true -> 竖屏状态， false->横屏状态
     */
    @Parcelize
    data class SplitWindowParameter(var windowWith: Int, var windowHeight: Int, var splitWindow: Boolean, var isUnfold: Boolean) : Parcelable {

        fun isSmallScreenMultiWindow(): Boolean {
            return splitWindow && (!isUnfold)
        }
    }
}