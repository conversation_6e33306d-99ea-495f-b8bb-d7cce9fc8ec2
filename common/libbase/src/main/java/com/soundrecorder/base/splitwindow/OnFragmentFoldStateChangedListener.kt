/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FoldingWindowObserver
 * Description:
 * Version: 1.0
 * Date: 2022/7/25
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/7/25 1.0 create
 */

package com.soundrecorder.base.splitwindow

interface OnFragmentFoldStateChangedListener {

    fun onFoldStateChanged(state: Int)
}