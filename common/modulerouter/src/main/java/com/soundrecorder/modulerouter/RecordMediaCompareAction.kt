package com.soundrecorder.modulerouter

import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object RecordMediaCompareAction {
    const val COMPONENT_NAME = "RecordMediaCompareAction"
    const val DO_MEDIA_COMPARE = "do_media_comapare"
    const val DO_STOP_MEDIA_COMPARE = "do_stop_media_comapare"
    const val IS_MEDIA_COMPARING = "is_media_comparing"

    private val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun doMediaCompare(trigCloudSyncRightNow: Boolean, syncType: Int = -1) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, DO_MEDIA_COMPARE)
                    .param(trigCloudSyncRightNow, syncType).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun doStopMediaCompare(stop: <PERSON><PERSON>an) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, DO_STOP_MEDIA_COMPARE)
                    .param(stop).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun isMediaComparing(): Boolean {
        return if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, IS_MEDIA_COMPARING)
                    .build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }
}