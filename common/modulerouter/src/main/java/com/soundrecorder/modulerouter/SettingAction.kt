/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SettingAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object SettingAction {
    const val COMPONENT_NAME = "Setting"
    const val LAUNCH_FOR_RESULT = "launchForResult"
    const val LAUNCH_BOOT_REG_PRIVACY = "launchBootRegPrivacy"
    const val LAUNCH_RECORD_PRIVACY = "launchRecordPrivacy"
    const val GET_PAGE_NAME_IN_SETTING = "getPageNameInSetting"
    const val GET_SHOW_RECORD_MODE_RED_DOT = "getShowRecordModeRedDot"
    const val SET_RECORD_MODE_RED_DOT_SHOWED = "setRecordModeRedDotShowed"
    const val IS_SUPPORT_AUDIO_MONITOR = "isSupportTripartiteAudioMonitor"
    const val LAUNCH_COLLECTION_INFO = "launchCollectionInfo"
    const val LAUNCH_COLLECTION_INFO_DETAILS = "launchCollectionInfoDetails"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun launchForResult(fragment: Fragment, requestCode: Int) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, LAUNCH_FOR_RESULT)
                .param(fragment, requestCode).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    /**
     * 跳转开机向导隐私政策页面
     */
    @JvmStatic
    fun launchBootRegPrivacy(activity: Activity?, disableDialogFun: ((dialog: AlertDialog) -> Unit)) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, LAUNCH_BOOT_REG_PRIVACY)
                .param(activity, disableDialogFun).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    /**
     * 收集个人信息明示清单-详情页
     */
    @JvmStatic
    fun launchCollectionInfo(activity: Activity?, type: Int) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, LAUNCH_COLLECTION_INFO)
                .param(activity, type).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    /**
     * 收集个人信息明示清单-详情页
     */
    @JvmStatic
    fun launchCollectionInfoDetails(activity: Activity?, title: String?, type: Int, collectionType: Int) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, LAUNCH_COLLECTION_INFO_DETAILS)
                    .param(activity, title, type, collectionType).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    /**
     * 录音隐私政策页面
     */
    @JvmStatic
    fun launchRecorderPrivacy(activity: Activity?, type: Int) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, LAUNCH_RECORD_PRIVACY)
                .param(activity, type).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    /**
     * 录音设置内部页面名称
     */
    @JvmStatic
    fun getPageNameInSetting(): Array<String>? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_PAGE_NAME_IN_SETTING).build()
            OStitch.execute<Array<String>>(apiRequest).result
        } else null
    }

    /**
     * 获取是否需要显示录制模式小红点
     */
    @JvmStatic
    fun getNeedShowRecordModeRedDot(): Boolean {
        return if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, GET_SHOW_RECORD_MODE_RED_DOT).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else false
    }

    /**
     * 获取是否需要显示录制模式小红点
     */
    @JvmStatic
    fun setRecordModeRedDotShowed() {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, SET_RECORD_MODE_RED_DOT_SHOWED).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    fun isSupportTripartiteAudioMonitor(): Boolean {
        return if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, IS_SUPPORT_AUDIO_MONITOR).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else false
    }
}