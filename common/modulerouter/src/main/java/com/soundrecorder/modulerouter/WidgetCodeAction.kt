/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: WidgetCodeApi
 Description:
 Version: 1.0
 Date: 2023/3/22
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2023/3/22 1.0 create
 */

package com.soundrecorder.modulerouter

import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object WidgetCodeAction {
    const val COMPONENT_NAME = "WidgetCodeAction"
    const val GET_CARD_TYPE = "getCardType"
    val hasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun String.getCardType(): Int {
        return if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_CARD_TYPE)
                .param(this).build()
            OStitch.execute<Int>(apiRequest).result ?: -1
        } else {
            -1
        }
    }
}