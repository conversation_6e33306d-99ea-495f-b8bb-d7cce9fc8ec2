/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveMarkAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.net.Uri
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object WaveMarkAction {
    const val COMPONENT_NAME = "WaveMark"
    const val DECODE_AMPLITUDE_BY_URI = "createPlaybackIntent"
    const val FUN_GET_MERGE_MARK_LIST = "getMergeMarkList"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun decodeAmplitudeByUri(uri: Uri?): String {
        return if (uri != null && mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, DECODE_AMPLITUDE_BY_URI)
                .param(uri).build()
            OStitch.execute<String>(apiRequest).result ?: ""
        } else {
            ""
        }
    }

    @JvmStatic
    fun getMergeMarkList(path: String, playUri: Uri, isRecycle: Boolean): List<Any>? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_GET_MERGE_MARK_LIST)
                .param(path, playUri, isRecycle).build()
            OStitch.execute<List<Any>>(apiRequest).result
        } else {
            null
        }
    }
}