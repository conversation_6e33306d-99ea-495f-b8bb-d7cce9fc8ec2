/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter

import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object CommonAction {
    const val COMPONENT_NAME = "Common"
    const val BURYING_POINT_HOVER = "BURYING_POINT_HOVER"
    const val BURYING_POINT_RECORD = "BURYING_POINT_RECORD"
    const val BURYING_POINT_RECORD_ENTRY = "BURYING_POINT_RECORD_ENTRY"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun hoverBuryingPoint(name: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, BURYING_POINT_HOVER)
                .param(name).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun useRecordDurationMessage(time: Long) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, BURYING_POINT_RECORD)
                .param(time).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun useRecordEntryLaunch(entryType: String, landingType: Int) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, BURYING_POINT_RECORD_ENTRY)
                .param(entryType, landingType).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }
}