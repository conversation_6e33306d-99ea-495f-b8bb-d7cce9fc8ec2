/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertTaskAction.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.convertService

import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object ConvertTaskAction {

    const val COMPONENT_NAME = "ConvertTaskAction"

    const val START_CONVERT_TASK = "startConvertTask"

    @JvmStatic
    fun startConvertTask(
        mediaId: Long
    ) {
        val apiRequest = ApiRequest.Builder(COMPONENT_NAME, START_CONVERT_TASK)
            .param(mediaId)
            .build()
        OStitch.execute<Unit>(apiRequest)
    }
}
