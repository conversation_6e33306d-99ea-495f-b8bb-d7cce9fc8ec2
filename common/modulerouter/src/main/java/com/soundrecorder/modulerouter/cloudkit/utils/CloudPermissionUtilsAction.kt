package com.soundrecorder.modulerouter.cloudkit.utils

import android.content.Context
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object CloudPermissionUtilsAction {

    const val COMPONENT_NAME = "CloudPermissionUtilsAction"
    //setCloudGrantedStatus
    const val ACTION_SET_CLOUD_STATUS = "action_set_cloud_status"
    //clearCloudGrantedStatus
    const val ACTION_CLEAR_CLOUD_GRANTED_STATUS = "action_clear_cloud_granted_status"
    //isStatementCloudGranted
    const val ACTION_IS_CLOUD_GRANTED = "action_is_cloud_granted"
    /*是否有云同步所需权限*/
    const val ACTION_HAS_CLOUD_REQUIRED_PERMISSION = "action_has_cloud_required_permission"
    const val ACTION_IS_NETWORK_GRANTED = "action_is_network_granted"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun setCloudGrantedStatus(context: Context) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_SET_CLOUD_STATUS)
                    .param(context)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun clearCloudGrantedStatus() {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CLEAR_CLOUD_GRANTED_STATUS).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun isStatementCloudGranted(context: Context): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_CLOUD_GRANTED)
                    .param(context)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun hasCloudRequiredPermissions(): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_HAS_CLOUD_REQUIRED_PERMISSION)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isNetWorkGranted(context: Context): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_NETWORK_GRANTED)
                    .param(context)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }
}