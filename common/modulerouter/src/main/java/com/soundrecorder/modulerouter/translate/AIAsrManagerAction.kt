/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AIAsrManagerAction
 * Description:
 * Version: 1.0
 * Date: 2025/3/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/26 1.0 create
 */

package com.soundrecorder.modulerouter.translate

import android.content.Context
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest
import com.soundrecorder.modulerouter.smartname.SmartNameAction.DETECT_UNIFIED_SUMMARY

object AIAsrManagerAction {
    const val COMPONENT_NAME = "AIAsrManagerAction"
    const val LOAD_AI_ASR_SUPPORT_STATE = "loadSupportAIAsr"
    const val NEW_ASR_DOWNLOAD_DELEGATE = "newAsrPluginDownloadDelegate"
    const val IS_DETECT_SUPPORTED = "isDetectSupported"
    const val CHECK_UPDATE_AIUNIT_CONFIG_BACKGROUND = "checkUpdateAIUnitConfigBackground"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun loadSupportAIAsr(context: Context, forceUpdate: Boolean = false): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, LOAD_AI_ASR_SUPPORT_STATE)
                .param(context, forceUpdate)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun newAsrPluginDownloadDelegate(): IAsrPluginDownloadDelegate? {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, NEW_ASR_DOWNLOAD_DELEGATE)
                .build()
            return OStitch.execute<IAsrPluginDownloadDelegate>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun isDetectSupported(context: Context, detectName: String = DETECT_UNIFIED_SUMMARY): Boolean? {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, IS_DETECT_SUPPORTED)
                .param(context, detectName)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result
        }
        return false
    }

    /**
     * check是否需要后台拉下aiunit配置信息
     */
    @JvmStatic
    fun checkUpdateAIUnitConfigBackground(context: Context) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CHECK_UPDATE_AIUNIT_CONFIG_BACKGROUND)
                    .param(context)
                    .build()
            OStitch.execute<Unit>(apiRequest)
        }
    }
}