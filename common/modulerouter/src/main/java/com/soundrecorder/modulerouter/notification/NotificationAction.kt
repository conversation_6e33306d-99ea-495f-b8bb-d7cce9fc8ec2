/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NotificationAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.notification

import android.app.Service
import android.content.Intent
import android.content.IntentFilter
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object NotificationAction {
    const val COMPONENT_NAME = "Notification"
    const val SHOW_NOTIFICATION = "showNotification"
    const val GET_GROUP_ID_BY_MODE_AND_PAGE = "getGroupIdByModeAndPage"
    const val GET_NOTIFICATION_ID_BY_MODE_AND_PAGE = "getNotificationIdByModeAndPage"
    const val GET_MODE_AND_PAGE_BY_NOTIFICATION_ID = "getModeAndPageByNotificationId"
    const val GET_NOTIFICATION_MODE = "getNotificationMode"
    const val CANCEL_NOTIFICATION_BY_PAGE = "cancelNotificationByPage"
    const val CANCEL_NOTIFICATION_MODE_AND_GROUP = "cancelNotificationModeAndGroup"
    const val CANCEL_ALL_NOTIFICATION = "cancelAllNotification"
    const val CANCEL_NOTIFICATION = "cancelNotification"
    const val GET_INTENT_FILTER = "getIntentFilter"
    const val IS_LOCK_SCREEN = "isLockScreen"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun showNotification(mode: Int?, page: Int?, notificationModel: NotificationModel?, service: Service? = null) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SHOW_NOTIFICATION)
                .param(mode, page, notificationModel, service).build()
            OStitch.execute<Intent>(apiRequest).result
        }
    }

    @JvmStatic
    fun getGroupIdByModeAndPage(mode: Int?, page: Int?): Int? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_GROUP_ID_BY_MODE_AND_PAGE)
                .param(mode, page).build()
            OStitch.execute<Int>(apiRequest).result
        } else null
    }

    @JvmStatic
    fun getNotificationIdByModeAndPage(mode: Int?, page: Int?): Int? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_NOTIFICATION_ID_BY_MODE_AND_PAGE)
                .param(mode, page).build()
            OStitch.execute<Int>(apiRequest).result
        } else null
    }

    @JvmStatic
    fun getModeAndPageByNotificationId(notificationId: Int): IntArray? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_MODE_AND_PAGE_BY_NOTIFICATION_ID)
                .param(notificationId).build()
            OStitch.execute<IntArray>(apiRequest).result
        } else null
    }

    @JvmStatic
    fun getNotificationMode(isFromOtherApp: Boolean): Int? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_NOTIFICATION_MODE)
                .param(isFromOtherApp).build()
            OStitch.execute<Int>(apiRequest).result
        } else null
    }

    /**
     * 取消通知，要求page相同
     * 目前用于录制页面取消掉与当前页面相同的播放通知
     * @param page 页面pageId
     */
    @JvmStatic
    fun cancelNotificationByPage(page: Int?) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CANCEL_NOTIFICATION_BY_PAGE)
                .param(page).build()
            OStitch.execute<Intent>(apiRequest).result
        }
    }

    /**
     * 取消通知，要求groupId相同&mode相同
     * 目前用于裁切页面取消掉与当前页面相同入口栈的播放通知
     * @param mode common/third
     * @param page 页面pageId
     */
    @JvmStatic
    fun cancelNotificationModeAndGroup(mode: Int?, page: Int?) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CANCEL_NOTIFICATION_MODE_AND_GROUP)
                .param(mode, page).build()
            OStitch.execute<Intent>(apiRequest).result
        }
    }

    @JvmStatic
    fun cancelAllNotification() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CANCEL_ALL_NOTIFICATION).build()
            OStitch.execute<Intent>(apiRequest).result
        }
    }

    /**
     * 取消通知
     * @param mode common/third
     * @param page 页面pageId
     */
    @JvmStatic
    fun cancelNotification(mode: Int?, page: Int?) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CANCEL_NOTIFICATION)
                .param(mode, page).build()
            OStitch.execute<Intent>(apiRequest).result
        }
    }

    @JvmStatic
    fun getIntentFilter(): IntentFilter? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_INTENT_FILTER).build()
            OStitch.execute<IntentFilter>(apiRequest).result
        } else null
    }

    @JvmStatic
    fun isLockScreen(): Boolean? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, IS_LOCK_SCREEN).build()
            OStitch.execute<Boolean>(apiRequest).result
        } else null
    }
}