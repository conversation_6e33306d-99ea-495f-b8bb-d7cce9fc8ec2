/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - QuestionShowCallback.kt
 ** Description: 问卷卡片显示的callback.
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: AAA@TAG1.TAG2.TAG3.TAG4[.TAG5.TAG6.TAG7]
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  AAA       2016/11/24     1.0     build this module
 **  BBB       2016/11/25     1.1     add some components
 ****************************************************************/
package com.soundrecorder.modulerouter.questionnaire

import android.view.View

interface QuestionShowCallback {

    companion object {

        /**
         * 正常show出来的回调默认值
         */
        const val SHOW_DEFAULT = -1

        /**
         * 7天超时原因
         */
        const val NOT_SHOW_REASON_7_DAY_EXCEED = 0

        /**
         * 网络请求访问错误
         */
        const val NOT_SHOW_REASON_REPOSITORY_FAILED = 1

        /**
         * 网络请求中value.size=0, 这种情况一般是填写并提交过问卷之后，再次调用网络请求时，返回数据为空
         */
        const val NOT_SHOW_REASON_VALUE_EMPTY = 2

        /**
         * 列表数据为空，不显示问卷页面
         */
        const val NOT_SHOW_REASON_CONTENT_LIST_EMPTY = 3
    }

    /**
     * 卡片显示或未显示的回调， show true的时候为显示， show为false的时候不显示， notShowReason 没有显示的原因
     */
    fun onShowStatusChange(cardView: View?, show: Boolean, notShowReason: Int)
}