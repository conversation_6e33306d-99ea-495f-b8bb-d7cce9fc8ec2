/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: HomeAction
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.modulerouter

import android.content.Context
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object HomeAction {

    const val COMPONENT_NAME = "HomeAction"

    const val IS_TRANSPARENT_ACTIVITY = "isTransparentActivity"
    const val GET_TRANSPARENT_CLASS = "getTransparentClass"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun isTransparentActivity(context: Context?): Boolean {
        return if (context != null && mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, IS_TRANSPARENT_ACTIVITY)
                .param(context).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun getTransParentActivityClass(): Class<*>? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_TRANSPARENT_CLASS
            ).build()
            OStitch.execute<Class<*>>(apiRequest).result
        } else null
    }
}