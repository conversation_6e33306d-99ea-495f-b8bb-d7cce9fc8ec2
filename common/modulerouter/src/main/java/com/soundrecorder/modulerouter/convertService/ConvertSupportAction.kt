/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ConvertSupportAction
 * Description:
 * Version: 1.0
 * Date: 2025/3/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/12 1.0 create
 */

package com.soundrecorder.modulerouter.convertService

import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object ConvertSupportAction {
    const val COMPONENT_NAME = "ConvertSupportAction"

    const val ACTION_IS_SUPPORT_CONVERT = "isSupportConvert"
    const val ACTION_SUPPORT_CONVERT_TYPE = "getConvertSupportType"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun isSupportConvert(fromMainProcess: Boolean): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_SUPPORT_CONVERT)
                .param(fromMainProcess)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun getConvertSupportType(fromMainProcess: Boolean): Int {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_SUPPORT_CONVERT_TYPE)
                .param(fromMainProcess)
                .build()
            return OStitch.execute<Int>(apiRequest).result ?: 0
        }
        return 0
    }
}