package com.soundrecorder.modulerouter.cloudkit.dialog

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object CloudSyncStatusDialogAction {
    const val COMPONENT_NAME = "CloudSyncStatusDialogAction"
    const val ACTION_NEW_STATUS_DIALOG = "action_new_status_dialog"
    const val ACTION_NEW_CLOUD_UPGRADE_HELPER = "action_new_cloud_upgrade_helper"
    const val ACTION_REQUEST_LOGIN = "action_request_login"
    const val ACTION_LAUNCH_CLOUD_APP = "action_launch_cloud_app"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    /**
     * 获取查看弹窗实例
     */
    @JvmStatic
    fun newCloudStatusDialog(mContext: Context, owner: LifecycleOwner?): ICloudSyncStatusDialog? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_NEW_STATUS_DIALOG)
                    .param(mContext, owner)
                    .build()
            return OStitch.execute<ICloudSyncStatusDialog>(apiRequest).result
        }
        return null
    }

    /**
     * 获取云空间升级弹窗实例
     */
    @JvmStatic
    fun newCloudUpgradeHelper(): ICloudUpgradeHelper? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_NEW_CLOUD_UPGRADE_HELPER)
                    .build()
            return OStitch.execute<ICloudUpgradeHelper>(apiRequest).result
        }
        return null
    }

    /**
     * 请求登录
     */
    @JvmStatic
    fun reqLogin(context: Context) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_REQUEST_LOGIN)
                    .param(context)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    /**
     * 启动录音快应用页面或者H5
     */
    @JvmStatic
    fun launchCloudApp(context: Context) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_LAUNCH_CLOUD_APP)
                    .param(context)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }
}