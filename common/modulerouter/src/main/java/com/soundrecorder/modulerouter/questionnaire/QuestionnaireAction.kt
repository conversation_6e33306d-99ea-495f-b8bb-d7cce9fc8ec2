/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: QuestionnaireAction
 Description:
 Version: 1.0
 Date: 2023/05/30 1.0
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2023/05/30 1.0 create
 */

package com.soundrecorder.modulerouter.questionnaire

import android.app.Application
import android.content.Context
import android.view.View
import android.view.ViewGroup
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest
import kotlinx.coroutines.CoroutineScope

object QuestionnaireAction {
    private const val COMPONENT_NAME = "Questionnaire"

    /*
    用户点击问卷上的忽略时的回调函数
   */
    const val ACTION_CARD_IGNORED = 1
    /*
    获取数据成功时调用回调函数
     */
    const val ACTION_CARD_SHOWN = 2
    /*
    获取数据失败时调用回调函数
     */
    const val ACTION_NO_DATA = 4
    /*
    用户完成问卷后的回调函数
     */
    const val ACTION_SURVEY_SUBMITTED = 3

    /**
     * 是否真实有问卷数据可显示（拉取数据不为空，但是可能存在用户已提交/忽略所有的问卷，导致没有问卷数据可以展示）
     */
    const val ACTION_DATA_VALID = 5


    const val ACTION_INIT_SDK = "initSDK"
    const val ACTION_GET_CDP_VIEW = "initView"
    const val ACTION_SET_CDP_CALLBACK = "setCDPCallback"
    const val ACTION_UPDATE_SPACE = "updateSpace"
    const val ACTION_RELEASE_SPACE = "releaseSpace"
    const val ACTION_VIEW_IS_VISIBLE = "actionViewVisible"

    private val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    /**
     * 初始化CDP SDK
     */
    fun initSDK(context: Application) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_INIT_SDK)
                .param(context).build()
            OStitch.execute<Boolean>(apiRequest)
        }
    }

    /**
     * 获取CdpView
     */
    fun initView(context: Context?, rootView: ViewGroup?): View? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_GET_CDP_VIEW)
                .param(context, rootView).build()
            OStitch.execute<View?>(apiRequest).result
        } else null
    }

    fun setCDPCallBack(cdpView: View, callback: QuestionCDPCallback?, animCallBack: QuestionCDPAnimCallback?) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_SET_CDP_CALLBACK)
                 .param(cdpView, callback, animCallBack).build()
            OStitch.execute<View?>(apiRequest)
        }
    }

    /**
     * 请求问卷数据并显示
     */
    fun updateSpace(coroutineScope: CoroutineScope, cdpView: View) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_UPDATE_SPACE)
                .param(coroutineScope, cdpView).build()
            OStitch.execute<Boolean>(apiRequest)
        }
    }

    /**
     * 判断当前mCdpView是否在显示
     */
    fun cdpViewIsVisible(cdpView: View): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_VIEW_IS_VISIBLE)
                .param(cdpView).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else false
    }

    /**
     * 释放资源
     */
    fun releaseSpace(cdpView: View?) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_RELEASE_SPACE)
                .param(cdpView).build()
            OStitch.execute<Boolean>(apiRequest)
        }
    }
}