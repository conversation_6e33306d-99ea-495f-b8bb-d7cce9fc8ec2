/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameCallback
 * * Description: SmartNameCallback
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.modulerouter.smartname

interface ISmartNameCallback {

    fun onSmartNameStart(mediaId: Long, extras: Map<String, Any>?) {
    }

    fun onSmartNameStop(mediaId: Long, extras: Map<String, Any>?) {
    }

    fun onSmartNameFinished(
        mediaId: Long,
        jsonResult: String,
        extras: Map<String, Any>?
    )
    fun onSmartNameError(mediaId: Long, errorCode: Int, errorMsg: String? = null)

    fun onSmartNameEnd(mediaId: Long) {
    }
}