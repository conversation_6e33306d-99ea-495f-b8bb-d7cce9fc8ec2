/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.convertService

import android.content.Context
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object ConvertCheckUtilsAction {

    const val COMPONENT_NAME = "ConvertCheckUtils"

    const val ACTION_IS_FILE_SIZE_MIN_MET = "isFileSizeMinMet"

    const val ACTION_IS_FILE_SIZE_MAX_MET = "isFileSizeMaxMet"

    const val ACTION_IS_FILE_DURATION_MIN_MET = "isFileDurationMinMet"

    const val ACTION_IS_FILE_DURATION_MAX_MET = "isFileDurationMaxMet"

    const val ACTION_IS_FILE_FORMAT_MET = "isFileFormatMet"

    const val GET_ERROR_MSG_BY_STATUS = "getErrorMsgByStatus"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun isFileSizeMinMet(fileSize: Long): Boolean {
        if (mHasComponent) {
            val apiRequest =
                    ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_FILE_SIZE_MIN_MET)
                            .param(fileSize)
                            .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isFileSizeMaxMet(fileSize: Long): Boolean {
        if (mHasComponent) {
            val apiRequest =
                    ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_FILE_SIZE_MAX_MET)
                            .param(fileSize)
                            .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isFileDurationMinMet(duration: Long): Boolean {
        if (mHasComponent) {
            val apiRequest =
                    ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_FILE_DURATION_MIN_MET)
                            .param(duration)
                            .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isFileDurationMaxMet(duration: Long): Boolean {
        if (mHasComponent) {
            val apiRequest =
                    ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_FILE_DURATION_MAX_MET)
                            .param(duration)
                            .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isFileFormatMet(fileFormat: String): Boolean {
        if (mHasComponent) {
            val apiRequest =
                    ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_FILE_FORMAT_MET)
                            .param(fileFormat)
                            .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun getErrorMsgByStatus(
        context: Context,
        uploadStatus: Int,
        convertStatus: Int,
        errorMessage: String
    ): String {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, GET_ERROR_MSG_BY_STATUS)
                    .param(context, uploadStatus, convertStatus, errorMessage)
                    .build()
            return OStitch.execute<String>(apiRequest).result ?: errorMessage
        }
        return errorMessage
    }
}