/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardAction
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/22 1.0 create
 */

package com.soundrecorder.modulerouter

import android.os.Bundle
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object AppCardAction {

    const val COMPONENT_NAME = "CardAction"
    // 旧卡编号需要与服务器上的配置对应，否则找不到卡片。
    const val CARD_TYPE_FOR_DRAGON_FLY = 777770016
    const val CARD_TYPE_FOR_SMALL_CARD = 222220090
    const val CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD = 222220121
    //速览卡上推荐的服务卡片旧卡编号，
    // const val CARD_TYPE_FOR_RECOMMEND_SMALL_CARD = 255461  // 测试环境
    const val CARD_TYPE_FOR_RECOMMEND_SMALL_CARD = 201565  // 正式环境
    const val ADD_WIDGET_CODES = "addWidgetCodes"
    const val ADD_WIDGET_CODES_ON_RESUME = "addWidgetCodesOnResume"
    const val REMOVE_WIDGET_CODES_ON_PAUSE = "removeWidgetCodeOnPause"
    const val CALL_FROM_DRAGON_FLY_CARD = "callFromDragonFlyCard"
    const val CALL_FROM_SMALL_CARD = "callFromSmallCard"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun addWidgetCodes(widgetCode: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ADD_WIDGET_CODES)
                .param(widgetCode).build()
            OStitch.execute<String>(apiRequest).result
        }
    }

    @JvmStatic
    fun addWidgetCodesOnResume(widgetCode: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ADD_WIDGET_CODES_ON_RESUME)
                .param(widgetCode).build()
            OStitch.execute<String>(apiRequest).result
        }
    }

    @JvmStatic
    fun removeWidgetCodeOnPause(widgetCode: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, REMOVE_WIDGET_CODES_ON_PAUSE)
                .param(widgetCode).build()
            OStitch.execute<String>(apiRequest).result
        }
    }

    @JvmStatic
    fun callFromDragonFlyCard(method: String, widgetCode: String): Bundle? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CALL_FROM_DRAGON_FLY_CARD)
                .param(method, widgetCode).build()
            OStitch.execute<Bundle>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun callFromSmallCard(method: String, widgetCode: String, isRecommendCard: Boolean = false): Bundle? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CALL_FROM_SMALL_CARD)
                .param(method, widgetCode, isRecommendCard).build()
            OStitch.execute<Bundle>(apiRequest).result
        } else {
            null
        }
    }
}