package com.soundrecorder.common.utils

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil

import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog


@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class MarkProcessUtilTest {

    companion object {
        val BEAN_ZERO = MarkDataBean(0L).apply { defaultNo = 0 }
        val BEAN_ONE = MarkDataBean(1000L).apply { defaultNo = 1 }
        val BEAN_TWO = MarkDataBean(2000L).apply { defaultNo = 2 }
        val BEAN_THREE = MarkDataBean(3000L).apply { defaultNo = 3 }
        val BEAN_FOUR = MarkDataBean(4000L).apply { defaultNo = 4 }
    }

    @Before
    fun setUp() {
    }

    @After
    fun tearDown() {
    }

    @Test
    fun should_not_null_when_mergeOldAndNewMarkList_different_inputs() {
        val oldMarkDataBean = arrayListOf(BEAN_ZERO, BEAN_ONE, BEAN_THREE)
        val newMarkDataBean = arrayListOf(BEAN_THREE, BEAN_TWO, BEAN_FOUR)
        var result: MutableList<MarkDataBean> =
            MarkProcessUtil.mergeOldAndNewMarkList(oldMarkDataBean, newMarkDataBean)
        assertNotNull(result)
        assertEquals(5, result.size)
        result = MarkProcessUtil.mergeOldAndNewMarkList(arrayListOf(), arrayListOf())
        assertNotNull(result)
        assertEquals(0, result.size)
    }

    @Test
    fun should_correct_when_diffMarkList_different_inputs() {
        val oldMarkDataBean = arrayListOf(BEAN_ZERO, BEAN_ONE, BEAN_TWO, BEAN_THREE)
        val newMarkDataBean = arrayListOf(BEAN_ZERO, BEAN_TWO, BEAN_FOUR)
        val result = MarkProcessUtil.diffMarkList(oldMarkDataBean, newMarkDataBean)
        assertNotNull(result)
        assertEquals(mutableListOf(BEAN_FOUR), result.addMarks)
        assertEquals(mutableListOf(BEAN_ZERO, BEAN_TWO), result.updateMarks)
        assertEquals(mutableListOf(BEAN_ONE, BEAN_THREE), result.deleteMarks)
    }
}